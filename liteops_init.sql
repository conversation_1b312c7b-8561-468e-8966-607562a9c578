/*
 Navicat Premium Data Transfer

 Source Server         : 127.0.0.1
 Source Server Type    : MySQL
 Source Server Version : 80100 (8.1.0)
 Source Host           : 127.0.0.1:3306
 Source Schema         : liteops

 Target Server Type    : MySQL
 Target Server Version : 80100 (8.1.0)
 File Encoding         : 65001

 Date: 18/04/2025 16:39:19
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for auth_group
-- ----------------------------
DROP TABLE IF EXISTS `auth_group`;
CREATE TABLE `auth_group` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(150) COLLATE utf8mb4_bin NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of auth_group
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for auth_group_permissions
-- ----------------------------
DROP TABLE IF EXISTS `auth_group_permissions`;
CREATE TABLE `auth_group_permissions` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `group_id` int NOT NULL,
  `permission_id` int NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `auth_group_permissions_group_id_permission_id_0cd325b0_uniq` (`group_id`,`permission_id`),
  KEY `auth_group_permissio_permission_id_84c5c92e_fk_auth_perm` (`permission_id`),
  CONSTRAINT `auth_group_permissio_permission_id_84c5c92e_fk_auth_perm` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`),
  CONSTRAINT `auth_group_permissions_group_id_b120cbf9_fk_auth_group_id` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of auth_group_permissions
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for auth_permission
-- ----------------------------
DROP TABLE IF EXISTS `auth_permission`;
CREATE TABLE `auth_permission` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_bin NOT NULL,
  `content_type_id` int NOT NULL,
  `codename` varchar(100) COLLATE utf8mb4_bin NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `auth_permission_content_type_id_codename_01ab375a_uniq` (`content_type_id`,`codename`),
  CONSTRAINT `auth_permission_content_type_id_2f476e4b_fk_django_co` FOREIGN KEY (`content_type_id`) REFERENCES `django_content_type` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=117 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of auth_permission
-- ----------------------------
BEGIN;
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (1, 'Can add log entry', 1, 'add_logentry');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (2, 'Can change log entry', 1, 'change_logentry');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (3, 'Can delete log entry', 1, 'delete_logentry');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (4, 'Can view log entry', 1, 'view_logentry');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (5, 'Can add permission', 2, 'add_permission');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (6, 'Can change permission', 2, 'change_permission');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (7, 'Can delete permission', 2, 'delete_permission');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (8, 'Can view permission', 2, 'view_permission');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (9, 'Can add group', 3, 'add_group');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (10, 'Can change group', 3, 'change_group');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (11, 'Can delete group', 3, 'delete_group');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (12, 'Can view group', 3, 'view_group');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (13, 'Can add user', 4, 'add_user');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (14, 'Can change user', 4, 'change_user');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (15, 'Can delete user', 4, 'delete_user');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (16, 'Can view user', 4, 'view_user');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (17, 'Can add content type', 5, 'add_contenttype');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (18, 'Can change content type', 5, 'change_contenttype');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (19, 'Can delete content type', 5, 'delete_contenttype');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (20, 'Can view content type', 5, 'view_contenttype');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (21, 'Can add session', 6, 'add_session');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (22, 'Can change session', 6, 'change_session');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (23, 'Can delete session', 6, 'delete_session');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (24, 'Can view session', 6, 'view_session');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (25, 'Can add GitLab凭据', 7, 'add_gitlabcredential');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (26, 'Can change GitLab凭据', 7, 'change_gitlabcredential');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (27, 'Can delete GitLab凭据', 7, 'delete_gitlabcredential');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (28, 'Can view GitLab凭据', 7, 'view_gitlabcredential');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (29, 'Can add 用户', 8, 'add_user');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (30, 'Can change 用户', 8, 'change_user');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (31, 'Can delete 用户', 8, 'delete_user');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (32, 'Can view 用户', 8, 'view_user');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (33, 'Can add 用户Token', 9, 'add_usertoken');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (34, 'Can change 用户Token', 9, 'change_usertoken');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (35, 'Can delete 用户Token', 9, 'delete_usertoken');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (36, 'Can view 用户Token', 9, 'view_usertoken');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (37, 'Can add 项目', 10, 'add_project');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (38, 'Can change 项目', 10, 'change_project');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (39, 'Can delete 项目', 10, 'delete_project');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (40, 'Can view 项目', 10, 'view_project');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (41, 'Can add Kubernetes凭据', 11, 'add_kubernetescredential');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (42, 'Can change Kubernetes凭据', 11, 'change_kubernetescredential');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (43, 'Can delete Kubernetes凭据', 11, 'delete_kubernetescredential');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (44, 'Can view Kubernetes凭据', 11, 'view_kubernetescredential');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (45, 'Can add 全局变量', 12, 'add_globalvariable');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (46, 'Can change 全局变量', 12, 'change_globalvariable');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (47, 'Can delete 全局变量', 12, 'delete_globalvariable');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (48, 'Can view 全局变量', 12, 'view_globalvariable');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (49, 'Can add GitLab Token凭据', 13, 'add_gitlabtokencredential');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (50, 'Can change GitLab Token凭据', 13, 'change_gitlabtokencredential');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (51, 'Can delete GitLab Token凭据', 13, 'delete_gitlabtokencredential');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (52, 'Can view GitLab Token凭据', 13, 'view_gitlabtokencredential');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (53, 'Can add Docker凭据', 14, 'add_dockercredential');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (54, 'Can change Docker凭据', 14, 'change_dockercredential');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (55, 'Can delete Docker凭据', 14, 'delete_dockercredential');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (56, 'Can view Docker凭据', 14, 'view_dockercredential');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (57, 'Can add 环境配置', 15, 'add_environment');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (58, 'Can change 环境配置', 15, 'change_environment');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (59, 'Can delete 环境配置', 15, 'delete_environment');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (60, 'Can view 环境配置', 15, 'view_environment');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (61, 'Can add 构建任务', 16, 'add_buildtask');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (62, 'Can change 构建任务', 16, 'change_buildtask');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (63, 'Can delete 构建任务', 16, 'delete_buildtask');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (64, 'Can view 构建任务', 16, 'view_buildtask');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (65, 'Can add Jenkins配置', 17, 'add_jenkinsconfig');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (66, 'Can change Jenkins配置', 17, 'change_jenkinsconfig');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (67, 'Can delete Jenkins配置', 17, 'delete_jenkinsconfig');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (68, 'Can view Jenkins配置', 17, 'view_jenkinsconfig');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (69, 'Can add Jenkins视图', 18, 'add_jenkinsview');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (70, 'Can change Jenkins视图', 18, 'change_jenkinsview');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (71, 'Can delete Jenkins视图', 18, 'delete_jenkinsview');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (72, 'Can view Jenkins视图', 18, 'view_jenkinsview');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (73, 'Can add django job', 19, 'add_djangojob');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (74, 'Can change django job', 19, 'change_djangojob');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (75, 'Can delete django job', 19, 'delete_djangojob');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (76, 'Can view django job', 19, 'view_djangojob');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (77, 'Can add django job execution', 20, 'add_djangojobexecution');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (78, 'Can change django job execution', 20, 'change_djangojobexecution');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (79, 'Can delete django job execution', 20, 'delete_djangojobexecution');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (80, 'Can view django job execution', 20, 'view_djangojobexecution');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (81, 'Can add 构建历史', 21, 'add_buildhistory');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (82, 'Can change 构建历史', 21, 'change_buildhistory');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (83, 'Can delete 构建历史', 21, 'delete_buildhistory');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (84, 'Can view 构建历史', 21, 'view_buildhistory');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (85, 'Can add 通知机器人', 22, 'add_notificationrobot');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (86, 'Can change 通知机器人', 22, 'change_notificationrobot');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (87, 'Can delete 通知机器人', 22, 'delete_notificationrobot');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (88, 'Can view 通知机器人', 22, 'view_notificationrobot');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (89, 'Can add 上线清单', 23, 'add_releaselist');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (90, 'Can change 上线清单', 23, 'change_releaselist');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (91, 'Can delete 上线清单', 23, 'delete_releaselist');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (92, 'Can view 上线清单', 23, 'view_releaselist');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (93, 'Can add 上线清单日志', 24, 'add_releaselog');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (94, 'Can change 上线清单日志', 24, 'change_releaselog');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (95, 'Can delete 上线清单日志', 24, 'delete_releaselog');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (96, 'Can view 上线清单日志', 24, 'view_releaselog');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (97, 'Can add 角色', 25, 'add_role');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (98, 'Can change 角色', 25, 'change_role');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (99, 'Can delete 角色', 25, 'delete_role');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (100, 'Can view 角色', 25, 'view_role');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (101, 'Can add 用户角色关联', 26, 'add_userrole');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (102, 'Can change 用户角色关联', 26, 'change_userrole');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (103, 'Can delete 用户角色关联', 26, 'delete_userrole');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (104, 'Can view 用户角色关联', 26, 'view_userrole');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (105, 'Can add 登录日志', 27, 'add_loginlog');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (106, 'Can change 登录日志', 27, 'change_loginlog');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (107, 'Can delete 登录日志', 27, 'delete_loginlog');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (108, 'Can view 登录日志', 27, 'view_loginlog');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (109, 'Can add 操作日志', 28, 'add_operationlog');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (110, 'Can change 操作日志', 28, 'change_operationlog');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (111, 'Can delete 操作日志', 28, 'delete_operationlog');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (112, 'Can view 操作日志', 28, 'view_operationlog');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (113, 'Can add SSH密钥凭据', 29, 'add_sshkeycredential');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (114, 'Can change SSH密钥凭据', 29, 'change_sshkeycredential');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (115, 'Can delete SSH密钥凭据', 29, 'delete_sshkeycredential');
INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES (116, 'Can view SSH密钥凭据', 29, 'view_sshkeycredential');
COMMIT;

-- ----------------------------
-- Table structure for auth_user
-- ----------------------------
DROP TABLE IF EXISTS `auth_user`;
CREATE TABLE `auth_user` (
  `id` int NOT NULL AUTO_INCREMENT,
  `password` varchar(128) COLLATE utf8mb4_bin NOT NULL,
  `last_login` datetime(6) DEFAULT NULL,
  `is_superuser` tinyint(1) NOT NULL,
  `username` varchar(150) COLLATE utf8mb4_bin NOT NULL,
  `first_name` varchar(150) COLLATE utf8mb4_bin NOT NULL,
  `last_name` varchar(150) COLLATE utf8mb4_bin NOT NULL,
  `email` varchar(254) COLLATE utf8mb4_bin NOT NULL,
  `is_staff` tinyint(1) NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `date_joined` datetime(6) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of auth_user
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for auth_user_groups
-- ----------------------------
DROP TABLE IF EXISTS `auth_user_groups`;
CREATE TABLE `auth_user_groups` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `group_id` int NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `auth_user_groups_user_id_group_id_94350c0c_uniq` (`user_id`,`group_id`),
  KEY `auth_user_groups_group_id_97559544_fk_auth_group_id` (`group_id`),
  CONSTRAINT `auth_user_groups_group_id_97559544_fk_auth_group_id` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`),
  CONSTRAINT `auth_user_groups_user_id_6a12ed8b_fk_auth_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of auth_user_groups
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for auth_user_user_permissions
-- ----------------------------
DROP TABLE IF EXISTS `auth_user_user_permissions`;
CREATE TABLE `auth_user_user_permissions` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `permission_id` int NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `auth_user_user_permissions_user_id_permission_id_14a6b632_uniq` (`user_id`,`permission_id`),
  KEY `auth_user_user_permi_permission_id_1fbb5f2c_fk_auth_perm` (`permission_id`),
  CONSTRAINT `auth_user_user_permi_permission_id_1fbb5f2c_fk_auth_perm` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`),
  CONSTRAINT `auth_user_user_permissions_user_id_a95ead1b_fk_auth_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of auth_user_user_permissions
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for build_history
-- ----------------------------
DROP TABLE IF EXISTS `build_history`;
CREATE TABLE `build_history` (
  `id` int NOT NULL AUTO_INCREMENT,
  `history_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  `build_number` int NOT NULL,
  `branch` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL,
  `commit_id` varchar(40) COLLATE utf8mb4_bin DEFAULT NULL,
  `version` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `status` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `requirement` longtext COLLATE utf8mb4_bin,
  `build_log` longtext COLLATE utf8mb4_bin,
  `stages` json NOT NULL,
  `build_time` json NOT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  `operator_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  `task_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `history_id` (`history_id`),
  UNIQUE KEY `build_history_task_id_build_number_8fc0b316_uniq` (`task_id`,`build_number`),
  KEY `build_history_operator_id_f43bdff4_fk_user_user_id` (`operator_id`),
  CONSTRAINT `build_history_operator_id_f43bdff4_fk_user_user_id` FOREIGN KEY (`operator_id`) REFERENCES `user` (`user_id`),
  CONSTRAINT `build_history_task_id_dfb7725d_fk_build_task_task_id` FOREIGN KEY (`task_id`) REFERENCES `build_task` (`task_id`)
) ENGINE=InnoDB AUTO_INCREMENT=126 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of build_history
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for build_task
-- ----------------------------
DROP TABLE IF EXISTS `build_task`;
CREATE TABLE `build_task` (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  `name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL,
  `description` longtext COLLATE utf8mb4_bin,
  `branch` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL,
  `stages` json NOT NULL,
  `notification_channels` json NOT NULL,
  `status` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL,
  `last_build_number` int NOT NULL,
  `total_builds` int NOT NULL,
  `success_builds` int NOT NULL,
  `failure_builds` int NOT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  `creator_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  `environment_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  `git_token_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  `project_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  `version` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `build_time` json NOT NULL DEFAULT (_utf8mb3'{}'),
  `requirement` longtext COLLATE utf8mb4_bin,
  `ssh_key_credential_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `task_id` (`task_id`),
  KEY `build_task_creator_id_e702c745_fk_user_user_id` (`creator_id`),
  KEY `build_task_environment_id_8f5e7798_fk_environment_environment_id` (`environment_id`),
  KEY `build_task_git_token_id_813ab2b1_fk_gitlab_to` (`git_token_id`),
  KEY `build_task_project_id_f92c80ac_fk_project_project_id` (`project_id`),
  KEY `build_task_ssh_key_credential_i_b3430503_fk_ssh_key_c` (`ssh_key_credential_id`),
  CONSTRAINT `build_task_creator_id_e702c745_fk_user_user_id` FOREIGN KEY (`creator_id`) REFERENCES `user` (`user_id`),
  CONSTRAINT `build_task_environment_id_8f5e7798_fk_environment_environment_id` FOREIGN KEY (`environment_id`) REFERENCES `environment` (`environment_id`),
  CONSTRAINT `build_task_git_token_id_813ab2b1_fk_gitlab_to` FOREIGN KEY (`git_token_id`) REFERENCES `gitlab_token_credential` (`credential_id`),
  CONSTRAINT `build_task_project_id_f92c80ac_fk_project_project_id` FOREIGN KEY (`project_id`) REFERENCES `project` (`project_id`),
  CONSTRAINT `build_task_ssh_key_credential_i_b3430503_fk_ssh_key_c` FOREIGN KEY (`ssh_key_credential_id`) REFERENCES `ssh_key_credential` (`credential_id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of build_task
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for django_admin_log
-- ----------------------------
DROP TABLE IF EXISTS `django_admin_log`;
CREATE TABLE `django_admin_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `action_time` datetime(6) NOT NULL,
  `object_id` longtext COLLATE utf8mb4_bin,
  `object_repr` varchar(200) COLLATE utf8mb4_bin NOT NULL,
  `action_flag` smallint unsigned NOT NULL,
  `change_message` longtext COLLATE utf8mb4_bin NOT NULL,
  `content_type_id` int DEFAULT NULL,
  `user_id` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `django_admin_log_content_type_id_c4bce8eb_fk_django_co` (`content_type_id`),
  KEY `django_admin_log_user_id_c564eba6_fk_auth_user_id` (`user_id`),
  CONSTRAINT `django_admin_log_content_type_id_c4bce8eb_fk_django_co` FOREIGN KEY (`content_type_id`) REFERENCES `django_content_type` (`id`),
  CONSTRAINT `django_admin_log_user_id_c564eba6_fk_auth_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`),
  CONSTRAINT `django_admin_log_chk_1` CHECK ((`action_flag` >= 0))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of django_admin_log
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for django_apscheduler_djangojob
-- ----------------------------
DROP TABLE IF EXISTS `django_apscheduler_djangojob`;
CREATE TABLE `django_apscheduler_djangojob` (
  `id` varchar(255) COLLATE utf8mb4_bin NOT NULL,
  `next_run_time` datetime(6) DEFAULT NULL,
  `job_state` longblob NOT NULL,
  PRIMARY KEY (`id`),
  KEY `django_apscheduler_djangojob_next_run_time_2f022619` (`next_run_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of django_apscheduler_djangojob
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for django_apscheduler_djangojobexecution
-- ----------------------------
DROP TABLE IF EXISTS `django_apscheduler_djangojobexecution`;
CREATE TABLE `django_apscheduler_djangojobexecution` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `status` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `run_time` datetime(6) NOT NULL,
  `duration` decimal(15,2) DEFAULT NULL,
  `finished` decimal(15,2) DEFAULT NULL,
  `exception` varchar(1000) COLLATE utf8mb4_bin DEFAULT NULL,
  `traceback` longtext COLLATE utf8mb4_bin,
  `job_id` varchar(255) COLLATE utf8mb4_bin NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_job_executions` (`job_id`,`run_time`),
  KEY `django_apscheduler_djangojobexecution_run_time_16edd96b` (`run_time`),
  CONSTRAINT `django_apscheduler_djangojobexecution_job_id_daf5090a_fk` FOREIGN KEY (`job_id`) REFERENCES `django_apscheduler_djangojob` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of django_apscheduler_djangojobexecution
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for django_content_type
-- ----------------------------
DROP TABLE IF EXISTS `django_content_type`;
CREATE TABLE `django_content_type` (
  `id` int NOT NULL AUTO_INCREMENT,
  `app_label` varchar(100) COLLATE utf8mb4_bin NOT NULL,
  `model` varchar(100) COLLATE utf8mb4_bin NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `django_content_type_app_label_model_76bd3d3b_uniq` (`app_label`,`model`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of django_content_type
-- ----------------------------
BEGIN;
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (1, 'admin', 'logentry');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (21, 'apps', 'buildhistory');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (16, 'apps', 'buildtask');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (14, 'apps', 'dockercredential');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (15, 'apps', 'environment');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (7, 'apps', 'gitlabcredential');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (13, 'apps', 'gitlabtokencredential');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (12, 'apps', 'globalvariable');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (17, 'apps', 'jenkinsconfig');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (18, 'apps', 'jenkinsview');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (11, 'apps', 'kubernetescredential');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (27, 'apps', 'loginlog');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (22, 'apps', 'notificationrobot');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (28, 'apps', 'operationlog');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (10, 'apps', 'project');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (23, 'apps', 'releaselist');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (24, 'apps', 'releaselog');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (25, 'apps', 'role');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (29, 'apps', 'sshkeycredential');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (8, 'apps', 'user');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (26, 'apps', 'userrole');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (9, 'apps', 'usertoken');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (3, 'auth', 'group');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (2, 'auth', 'permission');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (4, 'auth', 'user');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (5, 'contenttypes', 'contenttype');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (19, 'django_apscheduler', 'djangojob');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (20, 'django_apscheduler', 'djangojobexecution');
INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES (6, 'sessions', 'session');
COMMIT;

-- ----------------------------
-- Table structure for django_migrations
-- ----------------------------
DROP TABLE IF EXISTS `django_migrations`;
CREATE TABLE `django_migrations` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `app` varchar(255) COLLATE utf8mb4_bin NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_bin NOT NULL,
  `applied` datetime(6) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=57 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of django_migrations
-- ----------------------------
BEGIN;
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (1, 'contenttypes', '0001_initial', '2025-02-14 15:01:06.821513');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (2, 'auth', '0001_initial', '2025-02-14 15:01:07.012445');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (3, 'admin', '0001_initial', '2025-02-14 15:01:07.062036');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (4, 'admin', '0002_logentry_remove_auto_add', '2025-02-14 15:01:07.070486');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (5, 'admin', '0003_logentry_add_action_flag_choices', '2025-02-14 15:01:07.074531');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (6, 'apps', '0001_initial', '2025-02-14 15:01:07.269160');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (7, 'contenttypes', '0002_remove_content_type_name', '2025-02-14 15:01:07.311205');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (8, 'auth', '0002_alter_permission_name_max_length', '2025-02-14 15:01:07.329696');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (9, 'auth', '0003_alter_user_email_max_length', '2025-02-14 15:01:07.341395');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (10, 'auth', '0004_alter_user_username_opts', '2025-02-14 15:01:07.345684');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (11, 'auth', '0005_alter_user_last_login_null', '2025-02-14 15:01:07.366592');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (12, 'auth', '0006_require_contenttypes_0002', '2025-02-14 15:01:07.368223');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (13, 'auth', '0007_alter_validators_add_error_messages', '2025-02-14 15:01:07.373801');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (14, 'auth', '0008_alter_user_username_max_length', '2025-02-14 15:01:07.395045');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (15, 'auth', '0009_alter_user_last_name_max_length', '2025-02-14 15:01:07.416257');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (16, 'auth', '0010_alter_group_name_max_length', '2025-02-14 15:01:07.424339');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (17, 'auth', '0011_update_proxy_permissions', '2025-02-14 15:01:07.431132');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (18, 'auth', '0012_alter_user_first_name_max_length', '2025-02-14 15:01:07.450297');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (19, 'sessions', '0001_initial', '2025-02-14 15:01:07.461954');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (20, 'apps', '0002_environment_buildtask', '2025-02-14 17:06:18.582376');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (21, 'apps', '0003_jenkinsconfig', '2025-02-17 11:19:30.476102');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (22, 'apps', '0004_jenkinsconfig_username', '2025-02-17 13:12:11.927014');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (23, 'apps', '0005_jenkinsview', '2025-02-19 13:18:41.012073');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (24, 'apps', '0006_jenkinsview_view_type', '2025-02-19 13:44:11.095270');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (25, 'apps', '0007_alter_jenkinsview_view_type', '2025-02-19 14:38:01.822146');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (26, 'apps', '0008_environment_code_environment_status_environment_type', '2025-02-24 16:07:53.339055');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (27, 'apps', '0009_remove_environment_code_remove_environment_status', '2025-02-24 16:15:35.303393');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (28, 'apps', '0010_remove_globalvariable_creator_and_more', '2025-02-25 13:59:53.326603');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (29, 'apps', '0011_buildtask', '2025-02-26 15:21:09.409547');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (30, 'apps', '0012_remove_project_gitlab_credential_and_more', '2025-03-04 13:12:26.353472');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (31, 'apps', '0013_buildtask_version', '2025-03-05 16:31:33.399141');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (32, 'apps', '0014_remove_buildtask_retry_count_and_more', '2025-03-06 10:56:29.670258');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (33, 'django_apscheduler', '0001_initial', '2025-03-06 11:09:49.816089');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (34, 'django_apscheduler', '0002_auto_20180412_0758', '2025-03-06 11:09:49.843582');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (35, 'django_apscheduler', '0003_auto_20200716_1632', '2025-03-06 11:09:49.853542');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (36, 'django_apscheduler', '0004_auto_20200717_1043', '2025-03-06 11:09:49.965426');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (37, 'django_apscheduler', '0005_migrate_name_to_id', '2025-03-06 11:09:49.985302');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (38, 'django_apscheduler', '0006_remove_djangojob_name', '2025-03-06 11:09:50.008018');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (39, 'django_apscheduler', '0007_auto_20200717_1404', '2025-03-06 11:09:50.044565');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (40, 'django_apscheduler', '0008_remove_djangojobexecution_started', '2025-03-06 11:09:50.054476');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (41, 'django_apscheduler', '0009_djangojobexecution_unique_job_executions', '2025-03-06 11:09:50.067328');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (42, 'apps', '0015_remove_buildtask_next_run_time_and_more', '2025-03-06 11:18:23.572919');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (43, 'apps', '0016_remove_buildtask_retry_count_and_more', '2025-03-06 13:38:05.107185');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (44, 'apps', '0017_buildtask_requirement', '2025-03-06 14:39:18.680344');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (45, 'apps', '0018_alter_buildtask_requirement_buildhistory', '2025-03-06 15:33:32.137169');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (46, 'apps', '0019_buildtask_is_locked_buildtask_locked_by', '2025-03-11 17:10:24.445258');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (47, 'apps', '0020_remove_buildtask_is_locked_and_more', '2025-03-11 17:12:21.906643');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (48, 'apps', '0021_notificationrobot', '2025-03-13 10:24:20.492190');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (49, 'apps', '0022_notificationrobot_ip_list_notificationrobot_keywords_and_more', '2025-03-13 10:43:33.875533');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (50, 'apps', '0023_releaselist_releaselog', '2025-03-25 15:57:11.429873');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (51, 'apps', '0024_remove_releaselog_operator_remove_releaselog_release_and_more', '2025-03-25 17:02:15.431007');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (52, 'apps', '0025_role_userrole', '2025-03-25 17:16:01.592487');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (53, 'apps', '0026_loginlog', '2025-04-02 14:46:20.141243');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (54, 'apps', '0027_operationlog', '2025-04-03 15:24:17.246174');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (55, 'apps', '0028_delete_operationlog', '2025-04-03 15:27:41.539979');
INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES (56, 'apps', '0029_sshkeycredential_buildtask_ssh_key_credential', '2025-04-11 14:59:47.273080');
COMMIT;

-- ----------------------------
-- Table structure for django_session
-- ----------------------------
DROP TABLE IF EXISTS `django_session`;
CREATE TABLE `django_session` (
  `session_key` varchar(40) COLLATE utf8mb4_bin NOT NULL,
  `session_data` longtext COLLATE utf8mb4_bin NOT NULL,
  `expire_date` datetime(6) NOT NULL,
  PRIMARY KEY (`session_key`),
  KEY `django_session_expire_date_a5c62663` (`expire_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of django_session
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for docker_credential
-- ----------------------------
DROP TABLE IF EXISTS `docker_credential`;
CREATE TABLE `docker_credential` (
  `id` int NOT NULL AUTO_INCREMENT,
  `credential_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  `name` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `description` longtext COLLATE utf8mb4_bin,
  `registry` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `username` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `password` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  `creator_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `credential_id` (`credential_id`),
  KEY `docker_credential_creator_id_3bb37ecd_fk_user_user_id` (`creator_id`),
  CONSTRAINT `docker_credential_creator_id_3bb37ecd_fk_user_user_id` FOREIGN KEY (`creator_id`) REFERENCES `user` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of docker_credential
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for environment
-- ----------------------------
DROP TABLE IF EXISTS `environment`;
CREATE TABLE `environment` (
  `id` int NOT NULL AUTO_INCREMENT,
  `environment_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  `name` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `description` longtext COLLATE utf8mb4_bin,
  `create_time` datetime(6) DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  `creator_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  `type` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `environment_id` (`environment_id`),
  KEY `environment_creator_id_2f30820a_fk_user_user_id` (`creator_id`),
  CONSTRAINT `environment_creator_id_2f30820a_fk_user_user_id` FOREIGN KEY (`creator_id`) REFERENCES `user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of environment
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for gitlab_token_credential
-- ----------------------------
DROP TABLE IF EXISTS `gitlab_token_credential`;
CREATE TABLE `gitlab_token_credential` (
  `id` int NOT NULL AUTO_INCREMENT,
  `credential_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  `name` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `description` longtext COLLATE utf8mb4_bin,
  `token` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  `creator_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `credential_id` (`credential_id`),
  KEY `gitlab_token_credential_creator_id_d53c3666_fk_user_user_id` (`creator_id`),
  CONSTRAINT `gitlab_token_credential_creator_id_d53c3666_fk_user_user_id` FOREIGN KEY (`creator_id`) REFERENCES `user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of gitlab_token_credential
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for kubernetes_credential
-- ----------------------------
DROP TABLE IF EXISTS `kubernetes_credential`;
CREATE TABLE `kubernetes_credential` (
  `id` int NOT NULL AUTO_INCREMENT,
  `credential_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  `name` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `description` longtext COLLATE utf8mb4_bin,
  `kubeconfig` longtext COLLATE utf8mb4_bin,
  `create_time` datetime(6) DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  `creator_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `credential_id` (`credential_id`),
  KEY `kubernetes_credential_creator_id_0ee05206_fk_user_user_id` (`creator_id`),
  CONSTRAINT `kubernetes_credential_creator_id_0ee05206_fk_user_user_id` FOREIGN KEY (`creator_id`) REFERENCES `user` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of kubernetes_credential
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for login_log
-- ----------------------------
DROP TABLE IF EXISTS `login_log`;
CREATE TABLE `login_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `log_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  `ip_address` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `user_agent` longtext COLLATE utf8mb4_bin,
  `status` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL,
  `fail_reason` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL,
  `login_time` datetime(6) DEFAULT NULL,
  `user_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `log_id` (`log_id`),
  KEY `login_log_user_id_69642132_fk_user_user_id` (`user_id`),
  CONSTRAINT `login_log_user_id_69642132_fk_user_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=51 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of login_log
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for notification_robot
-- ----------------------------
DROP TABLE IF EXISTS `notification_robot`;
CREATE TABLE `notification_robot` (
  `id` int NOT NULL AUTO_INCREMENT,
  `robot_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  `type` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL,
  `name` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `webhook` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `secret` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `remark` longtext COLLATE utf8mb4_bin,
  `create_time` datetime(6) DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  `creator_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  `ip_list` json DEFAULT NULL,
  `keywords` json DEFAULT NULL,
  `security_type` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `robot_id` (`robot_id`),
  KEY `notification_robot_creator_id_de406276_fk_user_user_id` (`creator_id`),
  CONSTRAINT `notification_robot_creator_id_de406276_fk_user_user_id` FOREIGN KEY (`creator_id`) REFERENCES `user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of notification_robot
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for project
-- ----------------------------
DROP TABLE IF EXISTS `project`;
CREATE TABLE `project` (
  `id` int NOT NULL AUTO_INCREMENT,
  `project_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  `name` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `description` longtext COLLATE utf8mb4_bin,
  `category` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL,
  `repository` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  `creator_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `project_id` (`project_id`),
  KEY `project_creator_id_e70918ae_fk_user_user_id` (`creator_id`),
  CONSTRAINT `project_creator_id_e70918ae_fk_user_user_id` FOREIGN KEY (`creator_id`) REFERENCES `user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of project
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for role
-- ----------------------------
DROP TABLE IF EXISTS `role`;
CREATE TABLE `role` (
  `id` int NOT NULL AUTO_INCREMENT,
  `role_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  `name` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `description` longtext COLLATE utf8mb4_bin,
  `permissions` json DEFAULT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  `creator_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `role_id` (`role_id`),
  UNIQUE KEY `name` (`name`),
  KEY `role_creator_id_37780e7e_fk_user_user_id` (`creator_id`),
  CONSTRAINT `role_creator_id_37780e7e_fk_user_user_id` FOREIGN KEY (`creator_id`) REFERENCES `user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of role
-- ----------------------------
BEGIN;
INSERT INTO `role` (`id`, `role_id`, `name`, `description`, `permissions`, `create_time`, `update_time`, `creator_id`) VALUES (29, '333ec25423e04a4e96b4bb238de51cc3', '管理员', '系统管理员，拥有所有权限', '{\"data\": {\"operations\": [\"view\"], \"project_ids\": [], \"project_scope\": \"all\", \"environment_scope\": \"all\", \"environment_types\": []}, \"menu\": [\"/projects\", \"/projects/list\", \"/build\", \"/build/tasks\", \"/build/history\", \"/logs/login\", \"/user\", \"/user/list\", \"/user/role\", \"/credentials\", \"/environments\", \"/environments/list\", \"/release\", \"/system\", \"/system/notification\", \"/system/audit\", \"/system/backup\", \"/system/status\", \"/dashboard\", \"/logs/operation\", \"/logs\"], \"function\": {\"role\": [\"view\", \"create\", \"edit\", \"delete\", \"assign_permission\"], \"user\": [\"view\", \"create\", \"edit\", \"delete\", \"toggle_status\", \"reset_password\"], \"build\": [\"view\", \"create\", \"edit\", \"delete\", \"execute\", \"view_log\"], \"project\": [\"view\", \"create\", \"edit\", \"delete\"], \"release\": [\"view\", \"create\", \"edit\", \"delete\", \"approve\", \"execute\", \"rollback\"], \"build_task\": [\"view\", \"create\", \"edit\", \"delete\", \"execute\", \"view_log\", \"disable\"], \"credential\": [\"view\", \"create\", \"edit\", \"delete\"], \"logs_login\": [\"view\"], \"environment\": [\"view\", \"create\", \"edit\", \"delete\"], \"notification\": [\"view\", \"create\", \"edit\", \"delete\", \"test\"], \"build_history\": [\"view\", \"view_log\", \"rollback\"], \"build_approval\": [\"view\", \"request\", \"approve\"], \"logs_operation\": [\"view\"]}}', '2025-03-27 14:45:04.779759', '2025-04-09 12:00:30.795300', '9bfef5a1ee1d4054be9727934ad112es');
INSERT INTO `role` (`id`, `role_id`, `name`, `description`, `permissions`, `create_time`, `update_time`, `creator_id`) VALUES (30, '5575cfdc75dd4f8e9c5441359478314e', '开发人员', '开发人员，负责编写代码和构建', '{\"data\": {\"operations\": [\"view\"], \"project_ids\": [], \"project_scope\": \"custom\", \"environment_scope\": \"custom\", \"environment_types\": []}, \"menu\": [], \"function\": {\"build\": [], \"project\": [], \"release\": [\"view\", \"create\", \"edit\"], \"build_task\": [], \"credential\": [], \"environment\": [], \"notification\": [], \"build_history\": []}}', '2025-03-27 14:45:04.783696', '2025-04-17 09:42:38.079772', '9bfef5a1ee1d4054be9727934ad112es');
INSERT INTO `role` (`id`, `role_id`, `name`, `description`, `permissions`, `create_time`, `update_time`, `creator_id`) VALUES (31, 'ea78a0379d7d45559c4db69e38f07cd3', '测试人员', '测试人员，负责测试和验证', '{\"data\": {\"operations\": [\"view\"], \"project_ids\": [], \"project_scope\": \"custom\", \"environment_scope\": \"custom\", \"environment_types\": []}, \"menu\": [], \"function\": {\"build\": [\"view\", \"view_log\"], \"project\": [], \"release\": [\"view\", \"approve\"], \"build_history\": []}}', '2025-03-27 14:45:04.785872', '2025-04-17 09:43:16.944919', '9bfef5a1ee1d4054be9727934ad112es');
INSERT INTO `role` (`id`, `role_id`, `name`, `description`, `permissions`, `create_time`, `update_time`, `creator_id`) VALUES (32, '3aac992b37e441abbeb2c67a0c79f01f', '运维人员', '运维人员，负责部署和运维', '{\"data\": {\"operations\": [\"view\"], \"project_ids\": [], \"project_scope\": \"custom\", \"environment_scope\": \"custom\", \"environment_types\": []}, \"menu\": [], \"function\": {\"build\": [\"view\", \"execute\", \"view_log\"], \"project\": [], \"release\": [\"view\", \"approve\", \"execute\", \"rollback\"], \"environment\": [], \"notification\": [], \"build_history\": []}}', '2025-03-27 14:45:04.787850', '2025-04-17 09:44:08.530007', '9bfef5a1ee1d4054be9727934ad112es');
COMMIT;

-- ----------------------------
-- Table structure for ssh_key_credential
-- ----------------------------
DROP TABLE IF EXISTS `ssh_key_credential`;
CREATE TABLE `ssh_key_credential` (
  `id` int NOT NULL AUTO_INCREMENT,
  `credential_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  `name` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `description` longtext COLLATE utf8mb4_bin,
  `username` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `private_key` longtext COLLATE utf8mb4_bin,
  `passphrase` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  `creator_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `credential_id` (`credential_id`),
  KEY `ssh_key_credential_creator_id_c7396682_fk_user_user_id` (`creator_id`),
  CONSTRAINT `ssh_key_credential_creator_id_c7396682_fk_user_user_id` FOREIGN KEY (`creator_id`) REFERENCES `user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of ssh_key_credential
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  `username` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `name` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `password` varchar(128) COLLATE utf8mb4_bin DEFAULT NULL,
  `email` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL,
  `status` smallint DEFAULT NULL,
  `login_time` datetime(6) DEFAULT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of user
-- ----------------------------
BEGIN;
INSERT INTO `user` (`id`, `user_id`, `username`, `name`, `password`, `email`, `status`, `login_time`, `create_time`, `update_time`) VALUES (9, '9bfef5a1ee1d4054be9727934ad112es', 'admin', '管理员', '240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9', '<EMAIL>', 1, '2025-04-17 09:40:22.704382', '2025-03-26 11:41:20.549327', '2025-04-17 09:40:22.704579');
COMMIT;

-- ----------------------------
-- Table structure for user_role
-- ----------------------------
DROP TABLE IF EXISTS `user_role`;
CREATE TABLE `user_role` (
  `id` int NOT NULL AUTO_INCREMENT,
  `create_time` datetime(6) DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  `role_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  `user_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_role_user_id_role_id_a1d0951e_uniq` (`user_id`,`role_id`),
  KEY `user_role_role_id_6a11361a_fk_role_role_id` (`role_id`),
  CONSTRAINT `user_role_role_id_6a11361a_fk_role_role_id` FOREIGN KEY (`role_id`) REFERENCES `role` (`role_id`),
  CONSTRAINT `user_role_user_id_12d84374_fk_user_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of user_role
-- ----------------------------
BEGIN;
INSERT INTO `user_role` (`id`, `create_time`, `update_time`, `role_id`, `user_id`) VALUES (14, '2025-03-27 14:45:11.269249', '2025-03-27 14:45:11.269261', '333ec25423e04a4e96b4bb238de51cc3', '9bfef5a1ee1d4054be9727934ad112es');
COMMIT;

-- ----------------------------
-- Table structure for user_token
-- ----------------------------
DROP TABLE IF EXISTS `user_token`;
CREATE TABLE `user_token` (
  `id` int NOT NULL AUTO_INCREMENT,
  `token_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  `token` varchar(256) COLLATE utf8mb4_bin DEFAULT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `update_time` datetime(6) DEFAULT NULL,
  `user_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `token_id` (`token_id`),
  KEY `user_token_user_id_69e1f632_fk_user_user_id` (`user_id`),
  CONSTRAINT `user_token_user_id_69e1f632_fk_user_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of user_token
-- ----------------------------
BEGIN;
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
