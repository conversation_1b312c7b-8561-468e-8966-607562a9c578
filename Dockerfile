# =============================================================================
# LiteOps CI/CD Platform - Optimized Multi-stage Dockerfile
# =============================================================================
# 第一阶段：构建和工具安装阶段
# 使用 Debian slim 作为基础镜像，避免 Alpine 架构兼容性问题
FROM debian:bullseye-slim as builder

# 设置构建时的环境变量
ENV DEBIAN_FRONTEND=noninteractive \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1

# =============================================================================
# 系统基础配置 - 配置镜像源和安装基础工具
# =============================================================================
RUN set -eux; \
    # 配置阿里云镜像源以加速下载
    sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    # 更新包列表并安装基础系统工具
    apt-get update && \
    apt-get install -y --no-install-recommends \
        # Python 构建依赖
        python3.9 \
        python3.9-dev \
        python3.9-distutils \
        python3-pip \
        # 系统工具
        curl \
        wget \
        ca-certificates \
        gnupg \
        lsb-release \
        # 构建工具
        build-essential \
        # 网络和SSH工具
        openssh-client \
        rsync \
        # Web服务器
        nginx \
        # 其他必要工具
        procps \
        && \
    # 创建Python3符号链接确保兼容性
    ln -sf /usr/bin/python3.9 /usr/bin/python3 && \
    ln -sf /usr/bin/python3.9 /usr/bin/python && \
    # 升级pip到最新版本
    python3 -m pip install --upgrade pip && \
    # 清理APT缓存减少镜像体积
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# =============================================================================
# Python环境配置 - 配置pip镜像源
# =============================================================================
RUN set -eux; \
    # 配置pip使用阿里云镜像源加速包下载
    pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set install.trusted-host mirrors.aliyun.com

# =============================================================================
# SSH客户端配置 - 配置SSH免交互连接
# =============================================================================
RUN set -eux; \
    # 创建SSH配置目录
    mkdir -p /root/.ssh && \
    chmod 700 /root/.ssh && \
    # 配置SSH客户端跳过主机密钥验证（CI/CD环境需要）
    echo "StrictHostKeyChecking no" > /root/.ssh/config && \
    echo "UserKnownHostsFile /dev/null" >> /root/.ssh/config && \
    echo "LogLevel ERROR" >> /root/.ssh/config && \
    chmod 600 /root/.ssh/config

# =============================================================================
# Node.js环境配置 - 安装NVM用于Node.js版本管理
# =============================================================================
ENV NVM_DIR=/root/.nvm
RUN set -eux; \
    # 安装NVM (Node Version Manager)
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.3/install.sh | bash && \
    # 确保NVM脚本可以被bash加载
    echo 'export NVM_DIR="$HOME/.nvm"' >> /root/.bashrc && \
    echo '[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"' >> /root/.bashrc && \
    echo '[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh" --no-use' >> /root/.profile

# =============================================================================
# Java开发环境 - 安装JDK 8和Maven
# =============================================================================
# 复制JDK和Maven压缩包到临时目录
COPY jdk-8u211-linux-x64.tar.gz apache-maven-3.8.8-bin.tar.gz /tmp/

RUN set -eux; \
    # 创建Java和Maven安装目录
    mkdir -p /usr/local/java /usr/local/maven && \
    # 解压JDK到指定目录
    tar -xzf /tmp/jdk-8u211-linux-x64.tar.gz -C /usr/local/java && \
    # 解压Maven到指定目录
    tar -xzf /tmp/apache-maven-3.8.8-bin.tar.gz -C /usr/local/maven && \
    # 清理临时文件减少镜像体积
    rm -f /tmp/jdk-8u211-linux-x64.tar.gz /tmp/apache-maven-3.8.8-bin.tar.gz

# 复制Maven配置文件（包含阿里云镜像源配置）
COPY settings.xml /usr/local/maven/apache-maven-3.8.8/conf/

# =============================================================================
# 容器运行时环境 - 安装containerd、buildkit和nerdctl
# =============================================================================
# 定义容器工具版本
ENV NERDCTL_VERSION=2.0.0 \
    BUILDKIT_VERSION=0.13.0 \
    CONTAINERD_VERSION=1.7.15 \
    RUNC_VERSION=1.1.12

RUN set -eux; \
    # 安装containerd容器运行时
    curl -L https://github.com/containerd/containerd/releases/download/v${CONTAINERD_VERSION}/containerd-${CONTAINERD_VERSION}-linux-amd64.tar.gz \
      | tar -xz -C /usr/local/bin --strip-components=1 bin/containerd bin/containerd-shim bin/containerd-shim-runc-v2 bin/ctr && \
    # 安装runc容器运行时
    curl -L https://github.com/opencontainers/runc/releases/download/v${RUNC_VERSION}/runc.amd64 \
      -o /usr/local/sbin/runc && chmod +x /usr/local/sbin/runc && \
    # 安装buildkit构建工具
    curl -L https://github.com/moby/buildkit/releases/download/v${BUILDKIT_VERSION}/buildkit-v${BUILDKIT_VERSION}.linux-amd64.tar.gz \
      | tar -xz -C /usr/local && \
    # 安装nerdctl (Docker兼容的CLI工具)
    curl -L https://github.com/containerd/nerdctl/releases/download/v${NERDCTL_VERSION}/nerdctl-${NERDCTL_VERSION}-linux-amd64.tar.gz \
      | tar -xz -C /usr/local/bin && \
    # 创建docker命令的符号链接以保持兼容性
    ln -s /usr/local/bin/nerdctl /usr/local/bin/docker

# =============================================================================
# 第二阶段：运行时镜像
# 基于相同的基础镜像创建最终的运行时环境
# =============================================================================
FROM debian:bullseye-slim

# 设置运行时环境变量
ENV DEBIAN_FRONTEND=noninteractive \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    # Java环境变量
    JAVA_HOME=/usr/local/java/jdk1.8.0_211 \
    MAVEN_HOME=/usr/local/maven/apache-maven-3.8.8 \
    # NVM环境变量
    NVM_DIR=/root/.nvm \
    # 更新PATH环境变量
    PATH=/usr/local/java/jdk1.8.0_211/bin:/usr/local/maven/apache-maven-3.8.8/bin:/usr/local/bin:/usr/local/sbin:$PATH

# =============================================================================
# 运行时系统配置 - 安装运行时必需的包
# =============================================================================
RUN set -eux; \
    # 配置阿里云镜像源
    sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    # 安装运行时必需的包
    apt-get update && \
    apt-get install -y --no-install-recommends \
        # Python运行时
        python3.9 \
        python3-pip \
        # 系统工具
        curl \
        ca-certificates \
        # 网络和SSH工具
        openssh-client \
        rsync \
        # Web服务器
        nginx \
        # 进程管理工具
        procps \
        # Bash shell (用户要求)
        bash \
        && \
    # 创建Python符号链接
    ln -sf /usr/bin/python3.9 /usr/bin/python3 && \
    ln -sf /usr/bin/python3.9 /usr/bin/python && \
    # 清理包缓存
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# =============================================================================
# 从构建阶段复制必要的文件和工具
# =============================================================================
# 复制Python pip配置
COPY --from=builder /root/.pip /root/.pip

# 复制SSH配置
COPY --from=builder /root/.ssh /root/.ssh

# 复制NVM和Node.js环境
COPY --from=builder /root/.nvm /root/.nvm
COPY --from=builder /root/.bashrc /root/.bashrc
COPY --from=builder /root/.profile /root/.profile

# 复制Java环境
COPY --from=builder /usr/local/java /usr/local/java
COPY --from=builder /usr/local/maven /usr/local/maven

# 复制容器工具
COPY --from=builder /usr/local/bin/containerd* /usr/local/bin/
COPY --from=builder /usr/local/bin/ctr /usr/local/bin/
COPY --from=builder /usr/local/bin/nerdctl /usr/local/bin/
COPY --from=builder /usr/local/bin/docker /usr/local/bin/
COPY --from=builder /usr/local/sbin/runc /usr/local/sbin/
COPY --from=builder /usr/local/bin/buildctl /usr/local/bin/
COPY --from=builder /usr/local/bin/buildkitd /usr/local/bin/

# =============================================================================
# 应用程序配置
# =============================================================================
# 设置工作目录
WORKDIR /app

# 配置Nginx - 复制自定义配置文件
COPY web/nginx.conf /etc/nginx/sites-available/default
RUN ln -sf /etc/nginx/sites-available/default /etc/nginx/sites-enabled/default

# 复制前端构建文件到Nginx静态文件目录
COPY web/dist/ /usr/share/nginx/html/

# 安装Python依赖
COPY backend/requirements.txt /app/
RUN set -eux; \
    # 配置pip镜像源
    pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set install.trusted-host mirrors.aliyun.com && \
    # 安装Python依赖包
    pip install --no-cache-dir -r requirements.txt

# 复制后端应用代码
COPY backend/ /app/

# 创建日志目录
RUN mkdir -p /app/logs

# 复制启动脚本并设置执行权限
COPY docker-entrypoint.sh /app/
COPY ci-entrypoint-rootful.sh /usr/local/bin/
RUN chmod +x /app/docker-entrypoint.sh /usr/local/bin/ci-entrypoint-rootful.sh

# =============================================================================
# 容器配置
# =============================================================================
# 暴露端口
# 80: Nginx Web服务器端口
# 8900: Django后端API端口
EXPOSE 80 8900

# 设置容器入口点和默认命令
ENTRYPOINT ["/usr/local/bin/ci-entrypoint-rootful.sh"]
CMD ["/app/docker-entrypoint.sh"]