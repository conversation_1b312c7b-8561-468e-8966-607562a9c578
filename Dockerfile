# =============================================================================
# LiteOps CI/CD Platform Docker Image
# 多阶段构建优化版本 - 减少最终镜像体积
# =============================================================================

# -----------------------------------------------------------------------------
# 构建阶段：用于下载和准备所有依赖
# -----------------------------------------------------------------------------
FROM python:3.9.6-slim as builder

# 设置构建时的工作目录
WORKDIR /build

# 配置阿里云镜像源以加速包下载
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list

# 安装构建时需要的工具
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        curl \
        ca-certificates \
        tar \
        gzip \
    && rm -rf /var/lib/apt/lists/*

# 设置容器运行时版本变量
ENV NERDCTL_VERSION=2.0.0 \
    BUILDKIT_VERSION=0.13.0 \
    CONTAINERD_VERSION=1.7.15 \
    RUNC_VERSION=1.1.12

# 下载并解压JDK和Maven到构建目录
COPY jdk-8u211-linux-x64.tar.gz apache-maven-3.8.8-bin.tar.gz /build/
RUN mkdir -p java maven && \
    tar -xzf jdk-8u211-linux-x64.tar.gz -C java --strip-components=0 && \
    tar -xzf apache-maven-3.8.8-bin.tar.gz -C maven --strip-components=0 && \
    rm -f *.tar.gz

# 下载容器运行时组件
RUN mkdir -p containerd buildkit nerdctl runc && \
    # 下载containerd
    curl -L https://github.com/containerd/containerd/releases/download/v${CONTAINERD_VERSION}/containerd-${CONTAINERD_VERSION}-linux-amd64.tar.gz \
      | tar -xz -C containerd --strip-components=1 && \
    # 下载runc
    curl -L https://github.com/opencontainers/runc/releases/download/v${RUNC_VERSION}/runc.amd64 \
      -o runc/runc && chmod +x runc/runc && \
    # 下载buildkit
    curl -L https://github.com/moby/buildkit/releases/download/v${BUILDKIT_VERSION}/buildkit-v${BUILDKIT_VERSION}.linux-amd64.tar.gz \
      | tar -xz -C buildkit --strip-components=1 && \
    # 下载nerdctl
    curl -L https://github.com/containerd/nerdctl/releases/download/v${NERDCTL_VERSION}/nerdctl-${NERDCTL_VERSION}-linux-amd64.tar.gz \
      | tar -xz -C nerdctl

# 下载并安装NVM
RUN curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.3/install.sh | bash

# -----------------------------------------------------------------------------
# 运行阶段：最终的精简镜像
# -----------------------------------------------------------------------------
FROM python:3.9.6-slim as runtime

# 设置应用工作目录
WORKDIR /app

# 配置阿里云镜像源
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list

# 安装运行时必需的系统依赖（精简版本）
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        nginx \
        openssh-client \
        curl \
        rsync \
        ca-certificates \
        procps \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# 配置pip阿里云镜像源
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set install.trusted-host mirrors.aliyun.com

# -----------------------------------------------------------------------------
# SSH配置：用于CI/CD远程操作
# -----------------------------------------------------------------------------
RUN mkdir -p /root/.ssh && \
    chmod 700 /root/.ssh && \
    echo "StrictHostKeyChecking no" > /root/.ssh/config && \
    echo "UserKnownHostsFile /dev/null" >> /root/.ssh/config && \
    echo "LogLevel ERROR" >> /root/.ssh/config && \
    chmod 600 /root/.ssh/config

# -----------------------------------------------------------------------------
# Nginx配置：前端静态文件服务
# -----------------------------------------------------------------------------
COPY web/nginx.conf /etc/nginx/sites-available/default
RUN ln -sf /etc/nginx/sites-available/default /etc/nginx/sites-enabled/default

# -----------------------------------------------------------------------------
# 从构建阶段复制Java开发环境
# -----------------------------------------------------------------------------
COPY --from=builder /build/java/jdk1.8.0_211 /usr/local/java/jdk1.8.0_211
COPY --from=builder /build/maven/apache-maven-3.8.8 /usr/local/maven/apache-maven-3.8.8

# 复制Maven配置文件
COPY settings.xml /usr/local/maven/apache-maven-3.8.8/conf/

# -----------------------------------------------------------------------------
# 从构建阶段复制容器运行时组件
# -----------------------------------------------------------------------------
COPY --from=builder /build/containerd/bin/* /usr/local/bin/
COPY --from=builder /build/runc/runc /usr/local/sbin/
COPY --from=builder /build/buildkit/bin/* /usr/local/bin/
COPY --from=builder /build/nerdctl/nerdctl /usr/local/bin/

# 创建docker命令软链接（兼容性）
RUN ln -s /usr/local/bin/nerdctl /usr/local/bin/docker

# -----------------------------------------------------------------------------
# 从构建阶段复制NVM
# -----------------------------------------------------------------------------
COPY --from=builder /root/.nvm /root/.nvm

# -----------------------------------------------------------------------------
# 环境变量配置
# -----------------------------------------------------------------------------
ENV JAVA_HOME=/usr/local/java/jdk1.8.0_211 \
    MAVEN_HOME=/usr/local/maven/apache-maven-3.8.8 \
    NVM_DIR=/root/.nvm \
    PATH=$PATH:/usr/local/java/jdk1.8.0_211/bin:/usr/local/maven/apache-maven-3.8.8/bin

# -----------------------------------------------------------------------------
# 前端静态文件部署
# -----------------------------------------------------------------------------
COPY web/dist/ /usr/share/nginx/html/

# -----------------------------------------------------------------------------
# Python后端应用部署
# -----------------------------------------------------------------------------
# 先复制依赖文件并安装（利用Docker缓存层）
COPY backend/requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt

# 复制后端应用代码
COPY backend/ /app/

# 创建应用日志目录
RUN mkdir -p /app/logs

# -----------------------------------------------------------------------------
# 启动脚本配置
# -----------------------------------------------------------------------------
COPY docker-entrypoint.sh /app/
COPY ci-entrypoint-rootful.sh /usr/local/bin/
RUN chmod +x /app/docker-entrypoint.sh /usr/local/bin/ci-entrypoint-rootful.sh

# -----------------------------------------------------------------------------
# 容器配置
# -----------------------------------------------------------------------------
# 暴露服务端口：80(Nginx前端) 8900(Python后端API)
EXPOSE 80 8900

# 设置容器启动入口点
ENTRYPOINT ["/usr/local/bin/ci-entrypoint-rootful.sh"]

# 默认启动命令
CMD ["/app/docker-entrypoint.sh"]