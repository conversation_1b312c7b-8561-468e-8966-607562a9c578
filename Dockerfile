FROM python:3.9.6

# 设置工作目录
WORKDIR /app

# 设置阿里云镜像源
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list

# 安装Nginx、SSH和其他必要的系统依赖
RUN apt-get update && \
    apt-get install -y \
    nginx \
    openssh-client \
    curl \
    rsync \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* 

# 设置pip阿里云镜像源
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ \
    && pip config set install.trusted-host mirrors.aliyun.com

# 配置Nginx&SSH
RUN mkdir -p /root/.ssh && \
    chmod 700 /root/.ssh && \
    echo "StrictHostKeyChecking no" > /root/.ssh/config && \
    echo "UserKnownHostsFile /dev/null" >> /root/.ssh/config && \
    echo "LogLevel ERROR" >> /root/.ssh/config && \
    chmod 600 /root/.ssh/config

# 配置Nginx
COPY web/nginx.conf /etc/nginx/sites-available/default
RUN ln -sf /etc/nginx/sites-available/default /etc/nginx/sites-enabled/default

# 安装和配置NVM
# ENV NVM_DIR=/root/.nvm
RUN curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.3/install.sh | bash

# 安装JDK和Maven
COPY jdk-8u211-linux-x64.tar.gz apache-maven-3.8.8-bin.tar.gz /tmp/
RUN mkdir -p /usr/local/java /usr/local/maven && \
    tar -xzf /tmp/jdk-8u211-linux-x64.tar.gz -C /usr/local/java && \
    tar -xzf /tmp/apache-maven-3.8.8-bin.tar.gz -C /usr/local/maven && \
    rm /tmp/jdk-8u211-linux-x64.tar.gz /tmp/apache-maven-3.8.8-bin.tar.gz

# 安装 containerd + buildkit + nerdctl
ENV NERDCTL_VERSION=2.0.0 \
    BUILDKIT_VERSION=0.13.0 \
    CONTAINERD_VERSION=1.7.15 \
    RUNC_VERSION=1.1.12

# 1. containerd & buildkitd
RUN curl -L https://github.com/containerd/containerd/releases/download/v${CONTAINERD_VERSION}/containerd-${CONTAINERD_VERSION}-linux-amd64.tar.gz \
      | tar -xz -C /usr/local/bin --strip-components=1 bin/containerd bin/containerd-shim bin/containerd-shim-runc-v2 bin/ctr; \
    curl -L https://github.com/opencontainers/runc/releases/download/v${RUNC_VERSION}/runc.amd64 \
      -o /usr/local/sbin/runc && chmod +x /usr/local/sbin/runc; \
    # 2. buildkitd
    curl -L https://github.com/moby/buildkit/releases/download/v${BUILDKIT_VERSION}/buildkit-v${BUILDKIT_VERSION}.linux-amd64.tar.gz \
      | tar -xz -C /usr/local; \
    # 3. nerdctl
    curl -L https://github.com/containerd/nerdctl/releases/download/v${NERDCTL_VERSION}/nerdctl-${NERDCTL_VERSION}-linux-amd64.tar.gz \
      | tar -xz -C /usr/local/bin; \
    ln -s /usr/local/bin/nerdctl /usr/local/bin/docker

# 复制settings.xml
COPY settings.xml /usr/local/maven/apache-maven-3.8.8/conf

# 设置环境变量
ENV JAVA_HOME=/usr/local/java/jdk1.8.0_211 \
    MAVEN_HOME=/usr/local/maven/apache-maven-3.8.8 \
    PATH=$PATH:/usr/local/java/jdk1.8.0_211/bin:/usr/local/maven/apache-maven-3.8.8/bin

# 复制前端构建文件
COPY web/dist/ /usr/share/nginx/html/

# 复制后端依赖文件并安装
COPY backend/requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt

# 复制后端项目文件并创建日志目录
COPY backend/ /app/
RUN mkdir -p logs

# 暴露端口
EXPOSE 80 8900

# 复制启动脚本
COPY docker-entrypoint.sh /app/

# ---------- 启动脚本 ----------
COPY ci-entrypoint-rootful.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/ci-entrypoint-rootful.sh

ENTRYPOINT ["/usr/local/bin/ci-entrypoint-rootful.sh"]

# 启动命令
CMD ["/app/docker-entrypoint.sh"]