server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # 开启gzip
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 9;
    gzip_types text/plain text/css text/javascript application/json application/javascript application/x-javascript application/xml;
    gzip_vary on;
    gzip_disable "MSIE [1-6]\.";

    # 前端静态文件
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }

    # SSE日志流特殊配置 - 完全禁用缓冲
    location /api/build/logs/stream/ {
        proxy_pass http://localhost:8900/api/build/logs/stream/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 完全禁用缓冲
        proxy_buffering off;
        proxy_cache off;
        proxy_request_buffering off;
        
        # SSE专用头部
        proxy_set_header Connection '';
        proxy_set_header Cache-Control 'no-cache, no-store, must-revalidate';
        proxy_set_header Pragma 'no-cache';
        proxy_set_header Expires '0';
        
        # 超时设置
        proxy_read_timeout 24h;
        proxy_connect_timeout 5s;
        proxy_send_timeout 24h;
        
        # HTTP版本和传输编码
        proxy_http_version 1.1;
        chunked_transfer_encoding off;
        
        # 添加CORS头
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, OPTIONS';
        add_header Access-Control-Allow-Headers 'Cache-Control, Authorization';
    }

    # API请求代理到后端服务
    location /api/ {
        proxy_pass http://localhost:8900/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 一般API的缓冲配置
        proxy_buffering off;
        proxy_cache off;
        proxy_read_timeout 60s;
        proxy_connect_timeout 5s;
        proxy_send_timeout 60s;
        
        proxy_http_version 1.1;
    }

    # 错误页面
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
