<template>
  <div class="credentials-list">
    <div class="page-header">
      <a-row justify="space-between" align="middle">
        <a-col>
          <h2>凭据管理</h2>
        </a-col>
        <a-col>
          <a-button type="primary" @click="showCreateModal">
            <template #icon><PlusOutlined /></template>
            添加凭据
          </a-button>
        </a-col>
      </a-row>
    </div>

    <a-card>
      <a-tabs v-model:activeKey="activeTab">
        <a-tab-pane key="gitlab_token" tab="GitLab Token凭据">
          <a-table
            :columns="columns"
            :data-source="credentials"
            :loading="loading"
            :pagination="false"
            :locale="{ emptyText: '暂无数据' }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" @click="handleEdit(record)">编辑</a-button>
                  <a-popconfirm
                    title="确定要删除这个凭据吗？"
                    @confirm="handleDelete(record)"
                  >
                    <a-button type="link" danger>删除</a-button>
                  </a-popconfirm>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-tab-pane>

        <a-tab-pane key="docker" tab="Docker凭据">
          <a-table
            :columns="columns"
            :data-source="credentials"
            :loading="loading"
            :pagination="false"
            :locale="{ emptyText: '暂无数据' }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" @click="handleEdit(record)">编辑</a-button>
                  <a-popconfirm
                    title="确定要删除这个凭据吗？"
                    @confirm="handleDelete(record)"
                  >
                    <a-button type="link" danger>删除</a-button>
                  </a-popconfirm>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-tab-pane>

        <a-tab-pane key="kubernetes" tab="Kubernetes凭据">
          <a-table
            :columns="columns"
            :data-source="credentials"
            :loading="loading"
            :pagination="false"
            :locale="{ emptyText: '暂无数据' }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" @click="handleEdit(record)">编辑</a-button>
                  <a-popconfirm
                    title="确定要删除这个凭据吗？"
                    @confirm="handleDelete(record)"
                  >
                    <a-button type="link" danger>删除</a-button>
                  </a-popconfirm>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-tab-pane>

        <!-- 新增：SSH密钥凭据 Tab -->
        <a-tab-pane key="ssh_key" tab="SSH密钥凭据">
          <a-table
            :columns="columns"
            :data-source="credentials"
            :loading="loading"
            :pagination="false"
            :locale="{ emptyText: '暂无数据' }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" @click="handleEdit(record)">编辑</a-button>
                  <a-popconfirm
                    title="确定要删除这个凭据吗？"
                    @confirm="handleDelete(record)"
                  >
                    <a-button type="link" danger>删除</a-button>
                  </a-popconfirm>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-tab-pane>

      </a-tabs>
    </a-card>

    <!-- 凭据表单弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="editingCredential ? '编辑凭据' : '添加凭据'"
      @ok="handleModalOk"
      :confirmLoading="submitLoading"
      width="600px"
    >
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        layout="vertical"
      >
        <a-form-item label="凭据名称" name="name">
          <a-input v-model:value="formState.name" placeholder="请输入凭据名称" />
        </a-form-item>

        <a-form-item label="凭据描述" name="description">
          <a-textarea
            v-model:value="formState.description"
            placeholder="请输入凭据描述"
            :rows="2"
          />
        </a-form-item>

        <!-- GitLab Token凭据表单 -->
        <template v-if="activeTab === 'gitlab_token'">
          <a-form-item label="GitLab Token" name="token">
            <a-input-password v-model:value="formState.token" placeholder="请输入GitLab Token" />
          </a-form-item>
        </template>

        <!-- Docker凭据表单 -->
        <template v-if="activeTab === 'docker'">
          <a-form-item label="Registry地址" name="registry">
            <a-input v-model:value="formState.registry" placeholder="请输入Registry地址" />
          </a-form-item>
          <a-form-item label="用户名" name="username">
            <a-input v-model:value="formState.username" placeholder="请输入用户名" />
          </a-form-item>
          <a-form-item label="密码" name="password">
            <a-input-password v-model:value="formState.password" placeholder="请输入密码" />
          </a-form-item>
        </template>

        <!-- Kubernetes凭据表单 -->
        <template v-if="activeTab === 'kubernetes'">
          <a-form-item label="kubeconfig内容" name="kubeconfig">
            <a-textarea
              v-model:value="formState.kubeconfig"
              placeholder="请输入kubeconfig内容"
              :rows="8"
            />
          </a-form-item>
        </template>

        <!-- 新增：SSH密钥凭据表单 -->
        <template v-if="activeTab === 'ssh_key'">
          <a-form-item label="远程用户名" name="username">
            <a-input v-model:value="formState.username" placeholder="请输入远程服务器登录用户名" />
          </a-form-item>
          <a-form-item label="SSH私钥内容" name="private_key">
            <div style="display: flex; flex-direction: column; gap: 8px;">
              <a-textarea
                v-model:value="formState.private_key"
                placeholder="请输入完整的SSH私钥内容"
                :rows="8"
              />
              <a-upload
                name="file"
                :multiple="false"
                :showUploadList="false"
                :beforeUpload="handleUploadPrivateKey"
              >
                <a-button>
                  <template #icon><UploadOutlined /></template>
                  上传私钥文件
                </a-button>
              </a-upload>
            </div>
          </a-form-item>
          <a-form-item label="私钥密码 (可选)" name="passphrase">
            <a-input-password v-model:value="formState.passphrase" placeholder="如果私钥有密码保护，请输入密码" />
          </a-form-item>
        </template>

      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined, UploadOutlined } from '@ant-design/icons-vue';
import axios from 'axios';
import { checkPermission, hasFunctionPermission } from '../../utils/permission';

const activeTab = ref('gitlab_token');
const loading = ref(false);
const submitLoading = ref(false);
const credentials = ref([]);
const modalVisible = ref(false);
const editingCredential = ref(null);
const formRef = ref();

// 表格列定义
const baseColumns = [
  {
    title: '凭据名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '创建者',
    key: 'creator',
    customRender: ({ record }) => record.creator?.name || '未知',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'create_time',
    key: 'create_time',
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
  },
];

// Docker凭据特有列
const dockerColumns = [
  ...baseColumns.slice(0, 1),
  {
    title: 'Registry地址',
    dataIndex: 'registry',
    key: 'registry'
  },
  {
    title: 'Docker帐号',
    dataIndex: 'username',
    key: 'username'
  },
  ...baseColumns.slice(1)
];

// Kubernetes凭据特有列
const kubernetesColumns = [
  ...baseColumns
];

// 新增：SSH密钥凭据特有列 (可以包含用户名)
const sshKeyColumns = [
  ...baseColumns.slice(0, 1),
  {
    title: '远程用户名',
    dataIndex: 'username',
    key: 'username'
  },
  ...baseColumns.slice(1)
];

// 根据当前选中的标签页获取对应的列定义
const columns = computed(() => {
  switch (activeTab.value) {
    case 'gitlab_token':
      return baseColumns;
    case 'docker':
      return dockerColumns;
    case 'kubernetes':
      return kubernetesColumns;
    case 'ssh_key': // <-- 新增 SSH 密钥列
      return sshKeyColumns;
    default:
      return baseColumns;
  }
});

const formState = reactive({
  name: '',
  description: '',
  username: '',
  password: '',
  token: '',
  registry: '',
  kubeconfig: '',
  private_key: '', // <-- 新增 SSH 私钥
  passphrase: '', // <-- 新增 SSH 密码
  type: '',
});

const rules = computed(() => {
  const baseRules = {
    name: [{ required: true, message: '请输入凭据名称' }],
    description: [{ required: false, message: '请输入凭据描述' }],
  };

  // 根据不同的凭据类型返回不同的验证规则
  switch (activeTab.value) {
    case 'gitlab_token':
      return {
        ...baseRules,
        token: [{ required: !editingCredential.value, message: '请输入GitLab Token' }],
      };
    case 'docker':
      return {
        ...baseRules,
        registry: [{ required: true, message: '请输入Registry地址' }],
        username: [{ required: true, message: '请输入Docker用户名' }],
        password: [{ required: !editingCredential.value, message: '请输入Docker密码' }],
      };
    case 'kubernetes':
      return {
        ...baseRules,
        kubeconfig: [{ required: true, message: '请输入kubeconfig内容' }],
      };
    case 'ssh_key': // <-- 新增 SSH 密钥验证规则
      return {
        ...baseRules,
        username: [{ required: true, message: '请输入远程用户名' }],
        private_key: [{ required: !editingCredential.value, message: '请输入SSH私钥内容' }], // 编辑时不强制要求输入
        passphrase: [{ required: false }] // 密码是可选的
      };
    default:
      return baseRules;
  }
});

// 加载凭据列表
const loadCredentials = async () => {
  // 检查查看权限
  if (!checkPermission('credential', 'view')) {
    return;
  }

  loading.value = true;
  try {
    const token = localStorage.getItem('token');
    const response = await axios.get('/api/credentials/', {
      headers: {
        'Authorization': token
      },
      params: {
        type: activeTab.value
      }
    });

    if (response.data.code === 200) {
      // 为每条数据添加 key 属性
      credentials.value = response.data.data.map(item => ({
        ...item,
        key: item.credential_id
      }));
    } else {
      message.error(response.data.message || '加载凭据列表失败');
    }
  } catch (error) {
    message.error('加载凭据列表失败');
    console.error('Load credentials error:', error);
  } finally {
    loading.value = false;
  }
};

// 重置表单状态
const resetFormState = () => {
  Object.keys(formState).forEach(key => {
    formState[key] = '';
  });
  formState.type = activeTab.value;
};

// 显示创建模态框
const showCreateModal = () => {
  if (!checkPermission('credential', 'create')) {
    return;
  }
  editingCredential.value = null;
  resetFormState();
  modalVisible.value = true;
};

// 处理编辑
const handleEdit = (record) => {
  if (!checkPermission('credential', 'edit')) {
    return;
  }

  editingCredential.value = record;

  // 根据凭据类型设置表单值
  const commonFields = {
    name: record.name,
    description: record.description,
  };

  switch (activeTab.value) {
    case 'gitlab_token':
      Object.assign(formState, {
        ...commonFields,
        // 不回显token
      });
      break;
    case 'docker':
      Object.assign(formState, {
        ...commonFields,
        registry: record.registry,
        username: record.username,
      });
      break;
    case 'kubernetes':
      Object.assign(formState, {
        ...commonFields,
        // 不回显kubeconfig内容
      });
      break;
    case 'ssh_key': // <-- 新增 SSH 密钥编辑处理
      Object.assign(formState, {
        ...commonFields,
        username: record.username,
        // 不回显私钥和密码
      });
      break;
  }

  modalVisible.value = true;
};

// 处理删除
const handleDelete = async (record) => {
  if (!checkPermission('credential', 'delete')) {
    return;
  }

  try {
    const token = localStorage.getItem('token');
    const response = await axios.delete('/api/credentials/', {
      headers: {
        'Authorization': token
      },
      data: {
        credential_id: record.credential_id,
        type: activeTab.value
      }
    });

    if (response.data.code === 200) {
      message.success('删除成功');
      await loadCredentials();
    } else {
      message.error(response.data.message || '删除失败');
    }
  } catch (error) {
    message.error('删除失败');
    console.error('Delete credential error:', error);
  }
};

// 处理私钥文件上传
const handleUploadPrivateKey = (file) => {
  const reader = new FileReader();
  reader.onload = (e) => {
    formState.private_key = e.target.result;
    message.success('私钥文件已上传');
  };
  reader.onerror = () => {
    message.error('读取文件失败');
  };
  reader.readAsText(file);
  return false; // 阻止默认上传行为
};

// 处理模态框确认
const handleModalOk = async () => {
  try {
    await formRef.value.validate();
    submitLoading.value = true;

    const token = localStorage.getItem('token');
    const data = {
      name: formState.name,
      description: formState.description,
      type: activeTab.value,
    };

    // 根据凭据类型添加不同的字段
    switch (activeTab.value) {
      case 'gitlab_token':
        Object.assign(data, {
          token: formState.token,
        });
        break;
      case 'docker':
        Object.assign(data, {
          registry: formState.registry,
          username: formState.username,
          password: formState.password,
        });
        break;
      case 'kubernetes':
        Object.assign(data, {
          kubeconfig: formState.kubeconfig,
        });
        break;
      case 'ssh_key': // <-- 新增 SSH 密钥数据准备
        Object.assign(data, {
          username: formState.username,
          private_key: formState.private_key,
          passphrase: formState.passphrase,
        });
        break;
    }

    // 如果是编辑模式，添加凭据ID
    if (editingCredential.value) {
      data.credential_id = editingCredential.value.credential_id;
    }

    const response = await axios({
      method: editingCredential.value ? 'put' : 'post',
      url: '/api/credentials/',
      headers: {
        'Authorization': token
      },
      data
    });

    if (response.data.code === 200) {
      message.success(editingCredential.value ? '更新成功' : '创建成功');
      modalVisible.value = false;
      await loadCredentials();
    } else {
      throw new Error(response.data.message || (editingCredential.value ? '更新失败' : '创建失败'));
    }
  } catch (error) {
    message.error(error.response?.data?.message || error.message);
    console.error('Save credential error:', error);
  } finally {
    submitLoading.value = false;
  }
};

// 监听标签页切换
watch(activeTab, () => {
  loadCredentials();
});

onMounted(() => {
  // 检查凭据查看权限，但不阻止页面渲染
  // 实际加载凭据时仍会有权限检查
  if (!hasFunctionPermission('credential', 'view')) {
    message.warning('您没有凭据查看权限，部分功能可能受限');
  }
  loadCredentials();
});
</script>

<style scoped>
.credentials-list {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
}

:deep(.ant-card) {
  border-radius: 4px;
}

:deep(.ant-tabs-nav) {
  margin-bottom: 16px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
}
</style>