<template>
  <div class="dashboard">

    <a-row :gutter="12" class="stat-cards">
      <a-col :span="6">
        <a-card :loading="loading" :bordered="false">
          <template #title>
            <span>
              <ProjectOutlined /> 项目总数
            </span>
          </template>
          <div class="card-content">
            <h2>{{ stats.project_count || 0 }}</h2>
            <p class="card-subtitle">总构建: <span class="build-text">{{ stats.total_builds_count || 0 }}</span> | 任务: <span class="task-text">{{ stats.task_count || 0 }}</span></p>
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card :loading="loading" :bordered="false">
          <template #title>
            <span>
              <UserOutlined /> 用户总数
            </span>
          </template>
          <div class="card-content">
            <h2>{{ stats.user_count || 0 }}</h2>
            <p class="card-subtitle">环境数量: {{ stats.env_count || 0 }}</p>
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card :loading="loading" :bordered="false">
          <template #title>
            <span>
              <BuildOutlined /> 构建成功率
            </span>
          </template>
          <div class="card-content">
            <h2>{{ stats.success_rate || 0 }}%</h2>
            <p class="card-subtitle">最近7天: {{ stats.total_recent_builds || 0 }} 次构建</p>
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card :loading="loading" :bordered="false">
          <template #title>
            <span>
              <ClockCircleOutlined /> 今日构建
            </span>
          </template>
          <div class="card-content">
            <h2>{{ todayBuilds.length }}</h2>
            <p class="card-subtitle">成功: <span class="success-text">{{ successBuilds }}</span> | 失败: <span class="failed-text">{{ failedBuilds }}</span></p>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="12" class="chart-row">
      <a-col :span="16">
        <a-card title="构建任务趋势" :loading="trendLoading" :bordered="false">
          <div class="chart-container" ref="trendChartRef"></div>
        </a-card>
      </a-col>

      <a-col :span="8">
        <a-card title="项目类型分布" :loading="distributionLoading" :bordered="false">
          <div class="chart-container" ref="pieChartRef"></div>
        </a-card>
      </a-col>
    </a-row>

    <a-card title="最近构建任务" class="recent-builds" :loading="recentLoading" :bordered="false">
      <a-table
        :columns="buildColumns"
        :data-source="recentBuilds"
        :pagination="{ pageSize: 5, size: 'small' }"
        size="small"
        :scroll="{ x: 1000 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <span :class="['status-text', `status-${record.status}`]">
              {{ getStatusText(record.status) }}
            </span>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, onBeforeUnmount, nextTick } from 'vue';
// 导入图标组件 - 这些图标在模板中使用
// eslint-disable-next-line
import {
  ProjectOutlined,
  UserOutlined,
  BuildOutlined,
  ClockCircleOutlined
} from '@ant-design/icons-vue';
import axios from 'axios';
// 引入 ECharts
import * as echarts from 'echarts/core';
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  ToolboxComponent,
} from 'echarts/components';
import { LineChart, PieChart } from 'echarts/charts';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';

// 注册 ECharts 组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  ToolboxComponent,
  LineChart,
  PieChart,
  CanvasRenderer,
  UniversalTransition
]);

// 状态变量
const loading = ref(false);
const trendLoading = ref(false);
const distributionLoading = ref(false);
const recentLoading = ref(false);
// 移除 detailLoading, dateBuilds, selectedDate 因为它们在 G2 代码中也未被使用
// const detailLoading = ref(false);

// 数据变量
const stats = ref({});
const trendData = ref({ dates: [], success: [], failed: [] });
const distributionData = ref([]);
const recentBuilds = ref([]);
const todayBuilds = ref([]);
// const dateBuilds = ref([]);
// const selectedDate = ref('');

// 图表引用
const trendChartRef = ref(null);
const pieChartRef = ref(null);

// 图表实例
let trendChart = null;
let pieChart = null;

// 计算属性
const successBuilds = computed(() => {
  return todayBuilds.value.filter(build => build.status === 'success').length;
});

const failedBuilds = computed(() => {
  return todayBuilds.value.filter(build => build.status === 'failed').length;
});

// 表格列定义
const buildColumns = [
  {
    title: '任务名称',
    dataIndex: 'task_name',
    key: 'task_name',
    ellipsis: true,
    // width: 180,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    // width: 80,
    align: 'center',
  },
  {
    title: '分支',
    dataIndex: 'branch',
    key: 'branch',
    ellipsis: true,
    // width: 120,
  },
  {
    title: '版本',
    dataIndex: 'version',
    key: 'version',
    ellipsis: true,
    // width: 100,
  },
  {
    title: '环境',
    dataIndex: 'environment',
    key: 'environment',
    width: 80,
    ellipsis: true,
  },
  {
    title: '需求',
    dataIndex: 'requirement',
    key: 'requirement',
    ellipsis: true,
    // width: 120,
  },
  {
    title: '构建时间',
    dataIndex: 'start_time',
    key: 'start_time',
    // width: 150,
  },
  {
    title: '耗时',
    dataIndex: 'duration',
    key: 'duration',
    // width: 80,
    align: 'center',
  },
  {
    title: '构建人',
    dataIndex: 'operator',
    key: 'operator',
    // width: 100,
    ellipsis: true,
  },
];

// 注意：此函数已不再使用，但保留以备将来可能需要

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'success': '成功',
    'failed': '失败',
    'running': '运行中',
    'pending': '等待中',
    'terminated': '已终止'
  };
  return statusMap[status] || '未知';
};

// 获取首页统计数据
const fetchStats = async () => {
  loading.value = true;
  try {
    const response = await axios.get('/api/dashboard/stats/');
    if (response.data.code === 200) {
      stats.value = response.data.data;
    }
  } catch (error) {
    console.error('获取统计数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 获取构建趋势数据
const fetchTrendData = async () => {
  trendLoading.value = true;
  try {
    // 接口默认获取最近7天数据
    const response = await axios.get('/api/dashboard/build-trend/');
    if (response.data.code === 200) {
      trendData.value = response.data.data;
      // 确保数据结构完整
      if (!trendData.value.dates) trendData.value.dates = [];
      if (!trendData.value.success) trendData.value.success = [];
      if (!trendData.value.failed) trendData.value.failed = [];

      // 不在此处调用 renderTrendChart()
    }
  } catch (error) {
    console.error('获取构建趋势数据失败:', error);
  } finally {
    trendLoading.value = false;
  }
};

// 获取项目分布数据
const fetchDistributionData = async () => {
  distributionLoading.value = true;
  try {
    const response = await axios.get('/api/dashboard/project-distribution/');
    if (response.data.code === 200) {
      distributionData.value = response.data.data || [];

      // 不在此处调用 renderPieChart()
    }
  } catch (error) {
    console.error('获取项目分布数据失败:', error);
  } finally {
    distributionLoading.value = false;
  }
};

// 获取最近构建任务
const fetchRecentBuilds = async () => {
  recentLoading.value = true;
  try {
    const response = await axios.get('/api/dashboard/recent-builds/');
    if (response.data.code === 200) {
      recentBuilds.value = response.data.data;
    }
  } catch (error) {
    console.error('获取最近构建任务失败:', error);
  } finally {
    recentLoading.value = false;
  }
};

// 获取今日构建数据
const fetchTodayBuilds = async () => {
  const today = new Date().toISOString().split('T')[0];
  try {
    const response = await axios.get(`/api/dashboard/build-detail/?date=${today}`);
    if (response.data.code === 200) {
      todayBuilds.value = response.data.data;
    }
  } catch (error) {
    console.error('获取今日构建数据失败:', error);
  }
};

// 使用 ECharts 渲染构建趋势图表
const renderTrendChart = () => {
  if (!trendChartRef.value) {
    console.error('Trend chart DOM element not found');
    return;
  }

  // 初始化或获取 ECharts 实例
  if (!trendChart) {
    trendChart = echarts.init(trendChartRef.value);
  } else {
    trendChart.clear(); // 清除旧配置
  }

  const { dates, success, failed } = trendData.value;

  // 更柔和的颜色配置
  const successColor = 'rgba(115, 209, 61, 0.75)'; // 更浅的绿色
  const failedColor = 'rgba(247, 103, 107, 0.75)'; // 更浅的红色

  // ECharts 配置项
  const option = {
    animation: true,
    animationDuration: 800,
    animationEasing: 'cubicInOut',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#f0f0f0',
      borderWidth: 1,
      padding: [10, 12],
      textStyle: {
        fontSize: 12,
        color: 'rgba(0, 0, 0, 0.65)'
      },
      shadowColor: 'rgba(0, 0, 0, 0.05)',
      shadowBlur: 12,
      shadowOffsetX: 0,
      shadowOffsetY: 3,
      formatter: function(params) {
        let result = `<div style="font-weight: 500; margin-bottom: 8px;">${params[0].axisValue}</div>`;
        params.forEach(item => {
          const color = item.seriesName === '成功构建' ? successColor : failedColor;
          const style = `display:inline-block; width:8px; height:8px; margin-right:6px; border-radius:50%; background-color:${color};`;
          result += `<div style="margin: 3px 0">
            <span style="${style}"></span>
            <span style="font-size:12px; color:rgba(0,0,0,0.65)">${item.seriesName}：</span>
            <span style="font-weight:500; margin-left:4px; color:${color}">${item.value}</span>
          </div>`;
        });
        return result;
      },
      axisPointer: {
        type: 'line',
        lineStyle: {
          color: '#e8e8e8',
          type: 'dashed',
          width: 1
        }
      }
    },
    legend: {
      data: ['成功构建', '失败构建'],
      top: '2%',
      left: 'center',
      itemWidth: 14,
      itemHeight: 14,
      textStyle: {
        fontSize: 12,
        color: 'rgba(0, 0, 0, 0.65)'
      },
      icon: 'roundRect',
      itemGap: 20
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '5%',
      top: '16%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dates,
      axisLabel: {
        fontSize: 12,
        color: 'rgba(0, 0, 0, 0.65)',
        margin: 12
      },
      axisLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 12,
        color: 'rgba(0, 0, 0, 0.65)',
        margin: 12
      },
      splitLine: {
        lineStyle: {
          color: '#f5f5f5',
          type: 'dashed',
          width: 1
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    series: [
      {
        name: '成功构建',
        type: 'line',
        smooth: true,
        data: success,
        symbol: 'emptyCircle',
        symbolSize: 6,
        showSymbol: false,
        emphasis: {
          focus: 'series',
          scale: true,
          itemStyle: {
            color: successColor,
            borderColor: '#fff',
            borderWidth: 2,
            shadowColor: 'rgba(0, 0, 0, 0.1)',
            shadowBlur: 10
          },
          lineStyle: {
            width: 3
          }
        },
        // 鼠标悬停时显示符号
        showAllSymbol: 'auto',
        // 有数据的地方才显示符号
        showSymbol: false,
        lineStyle: {
          width: 3,
          shadowColor: 'rgba(115, 209, 61, 0.1)',
          shadowBlur: 8,
          shadowOffsetY: 4,
          cap: 'round'
        },
        itemStyle: {
          color: successColor,
          borderWidth: 2,
          borderColor: '#fff'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(115, 209, 61, 0.18)'
            }, {
              offset: 1, color: 'rgba(115, 209, 61, 0.02)'
            }]
          },
          opacity: 0.7
        }
      },
      {
        name: '失败构建',
        type: 'line',
        smooth: true,
        data: failed,
        symbol: 'emptyCircle',
        symbolSize: 6,
        showSymbol: false,
        emphasis: {
          focus: 'series',
          scale: true,
          itemStyle: {
            color: failedColor,
            borderColor: '#fff',
            borderWidth: 2,
            shadowColor: 'rgba(0, 0, 0, 0.1)',
            shadowBlur: 10
          },
          lineStyle: {
            width: 3
          }
        },
        // 有数据的地方才显示符号
        showAllSymbol: 'auto',
        // 鼠标悬停时显示符号
        showSymbol: false,
        lineStyle: {
          width: 3,
          shadowColor: 'rgba(247, 103, 107, 0.1)',
          shadowBlur: 8,
          shadowOffsetY: 4,
          cap: 'round'
        },
        itemStyle: {
          color: failedColor,
          borderWidth: 2,
          borderColor: '#fff'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(247, 103, 107, 0.18)'
            }, {
              offset: 1, color: 'rgba(247, 103, 107, 0.02)'
            }]
          },
          opacity: 0.7
        }
      }
    ]
  };

  // 应用配置项
  trendChart.setOption(option);

  // 添加图表交互事件
  trendChart.on('mouseover', { seriesIndex: 0 }, function() {
    trendChart.setOption({
      series: [{
        showSymbol: true
      }, {
        lineStyle: {
          opacity: 0.4
        },
        areaStyle: {
          opacity: 0.2
        }
      }]
    });
  });

  trendChart.on('mouseover', { seriesIndex: 1 }, function() {
    trendChart.setOption({
      series: [{
        lineStyle: {
          opacity: 0.4
        },
        areaStyle: {
          opacity: 0.2
        }
      }, {
        showSymbol: true
      }]
    });
  });

  trendChart.on('mouseout', function() {
    trendChart.setOption({
      series: [{
        showSymbol: false,
        lineStyle: {
          opacity: 1
        },
        areaStyle: {
          opacity: 0.7
        }
      }, {
        showSymbol: false,
        lineStyle: {
          opacity: 1
        },
        areaStyle: {
          opacity: 0.7
        }
      }]
    });
  });

  // 添加窗口大小调整监听
  window.addEventListener('resize', handleResize);
};

// 使用 ECharts 渲染项目分布图表
const renderPieChart = () => {
  if (!pieChartRef.value) {
    console.error('Pie chart DOM element not found');
    return;
  }

  if (!distributionData.value || distributionData.value.length === 0) {
    // console.warn('项目分布数据为空，不渲染饼图');
    // 可以选择显示提示信息或保持空白
     if (pieChart) {
       pieChart.clear(); // 清除旧图表
     }
    return;
  }

  // 初始化或获取 ECharts 实例
  if (!pieChart) {
    pieChart = echarts.init(pieChartRef.value);
  } else {
    pieChart.clear(); // 清除旧配置
  }


  // 格式化数据以符合 ECharts 要求
  const pieData = distributionData.value.map((item, index) => {
    // 预定义一组更柔和、更淡雅的颜色方案
    const colors = [
      'rgba(24, 144, 255, 0.6)',   // 更柔和的蓝色
      'rgba(82, 196, 26, 0.6)',    // 更柔和的绿色
      'rgba(47, 84, 235, 0.6)',    // 更柔和的靛蓝
      'rgba(19, 194, 194, 0.6)',   // 更柔和的青色
      'rgba(250, 173, 20, 0.6)',   // 更柔和的黄色
      'rgba(114, 46, 209, 0.6)',   // 更柔和的紫色
      'rgba(235, 47, 150, 0.6)'    // 更柔和的玫红
    ];

    return {
      name: item.type, // 'type' 字段包含 '前端项目', '后端项目' 等
      value: item.value,
      itemStyle: {
        color: colors[index % colors.length]
      }
    };
  });

  // ECharts 配置项
  const option = {
    animation: true,
    animationDuration: 600,
    animationEasing: 'cubicOut',
    animationDelay: 0,
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} ({d}%)',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#f0f0f0',
      borderWidth: 1,
      padding: [8, 12],
      textStyle: {
        fontSize: 12,
        color: 'rgba(0, 0, 0, 0.75)'
      },
      shadowColor: 'rgba(0, 0, 0, 0.05)',
      shadowBlur: 8,
      shadowOffsetX: 0,
      shadowOffsetY: 2
    },
    legend: {
      orient: 'vertical',
      left: '5%',
      top: 'center',
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        fontSize: 12,
        color: 'rgba(0, 0, 0, 0.65)'
      },
      icon: 'circle'
    },
    series: [
      {
        name: '项目类型分布',
        type: 'pie',
        radius: ['45%', '75%'],
        center: ['65%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: 'rgba(255, 255, 255, 0.8)',
          borderWidth: 4
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          scale: false,  // 禁用缩放效果
          scaleSize: 0,  // 设置缩放大小为0
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold',
            color: 'rgba(0, 0, 0, 0.85)'
          },
          itemStyle: {
            borderWidth: 1,
            borderColor: 'rgba(255, 255, 255, 0.8)',
            shadowBlur: 0,     // 去除阴影模糊
            shadowColor: 'transparent',  // 设置阴影颜色为透明
            shadowOffsetX: 0,  // 去除阴影偏移
            shadowOffsetY: 0   // 去除阴影偏移
          }
        },
        labelLine: {
          show: false
        },
        data: pieData
      }
    ]
  };

  // 应用配置项
  pieChart.setOption(option);

  // 添加窗口大小调整监听
  window.addEventListener('resize', handleResize);
};

// 处理窗口大小调整
const handleResize = () => {
  if (trendChart) {
    trendChart.resize();
  }
  if (pieChart) {
    pieChart.resize();
  }
};

// 组件卸载前清理资源
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  if (trendChart) {
    trendChart.dispose();
    trendChart = null;
  }
  if (pieChart) {
    pieChart.dispose();
    pieChart = null;
  }
});

// 页面加载时获取数据
onMounted(async () => {
  // 这些可以并行获取
  fetchStats();
  fetchRecentBuilds();
  fetchTodayBuilds();

  // 等待图表所需的数据获取完成
  await Promise.all([
    fetchTrendData(),
    fetchDistributionData()
  ]);

  // 此时组件已挂载，数据已获取，DOM 应该已准备好
  // 使用 nextTick 确保万无一失
  await nextTick();
  renderTrendChart();
  renderPieChart();
});
</script>

<style scoped>
.dashboard {
  /* padding: 16px; */
  color: rgba(0, 0, 0, 0.85); /* 主要文字颜色 - 深黑色 */
}

.card-content {
  text-align: center;
}

.card-content h2 {
  font-size: 22px;
  margin-bottom: 4px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85); /* 改为黑色 */
}

.card-content p {
  margin: 0;
  font-size: 13px;
  color: rgba(0, 0, 0, 0.45); /* 浅色黑色 */
}

.card-subtitle {
  margin-top: 4px;
}

.success-text {
  color: #52c41a;
  font-weight: 500;
}

.failed-text {
  color: #f5222d;
  font-weight: 500;
}

.task-text {
  font-weight: 500;
}

.build-text {
  font-weight: 500;
}

.chart-row {
  margin-top: 12px;
}

.chart-container {
  height: 320px;
  width: 100%;
}

.recent-builds {
  margin-top: 12px;
}

.stat-cards .ant-card {
  height: 100%;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.stat-cards .ant-card:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.stat-cards .ant-card-head-title {
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.65); /* 浅色黑色 */
}

/* 表格样式优化 */
:deep(.ant-table) {
  font-size: 13px;
}

:deep(.ant-table-thead > tr > th) {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.65);
  font-size: 13px;
  background-color: rgba(0, 0, 0, 0.02);
}

:deep(.ant-table-tbody > tr > td) {
  color: rgba(0, 0, 0, 0.75);
}

:deep(.ant-table-tbody > tr:hover > td) {
  background-color: rgba(0, 0, 0, 0.02);
}

:deep(.ant-card-head-title) {
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.75);
}

:deep(.ant-card) {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  transition: all 0.3s;
}

:deep(.ant-card:hover) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

:deep(.ant-pagination-item-link) {
  font-size: 12px;
}

:deep(.ant-pagination-item) {
  font-size: 12px;
  min-width: 28px;
  height: 28px;
  line-height: 26px;
}

.status-text {
  font-weight: 400;
  font-size: 13px;
}

.status-success {
  color: #52c41a; /* 绿色 - 成功 */
}
.status-failed {
  color: #f5222d; /* 红色 - 失败 */
}
.status-running {
  color: #1890ff; /* 蓝色 - 运行中 */
}
.status-pending {
  color: #faad14; /* 黄色 - 等待中 */
}
.status-terminated {
  color: rgba(0, 0, 0, 0.45); /* 灰色 - 已终止 */
}
</style>