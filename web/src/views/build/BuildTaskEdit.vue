<template>
  <div class="build-task-edit">
    <div class="page-header">
      <a-page-header
        :title="isEdit ? '编辑构建任务' : '新建构建任务'"
        :breadcrumb="{ routes }"
        @back="handleBack"
      />
    </div>

    <a-form
      :model="formState"
      :rules="rules"
      ref="formRef"
      layout="vertical"
    >
      <a-card title="基本信息" class="card-wrapper">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="任务名称" name="name" required>
              <a-input v-model:value="formState.name" placeholder="请输入任务名称" />
              <div class="form-item-help">任务名称将作为 Jenkins Job 名称，不能包含特殊字符</div>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="所属项目" name="project_id" required>
              <a-select
                v-model:value="formState.project_id"
                placeholder="请选择项目"
                :options="projectOptions"
                @change="handleProjectChange"
                show-search
                :filter-option="filterOption"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="构建环境" name="environment_id" required>
              <a-select
                v-model:value="formState.environment_id"
                placeholder="请选择环境"
                :options="environmentOptions"
                show-search
                :filter-option="filterOption"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="描述" name="description">
              <a-textarea
                v-model:value="formState.description"
                placeholder="请输入任务描述"
                :rows="4"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>

      <a-card title="源码配置" class="card-wrapper">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="默认分支" name="branch">
              <a-input
                v-model:value="formState.branch"
                placeholder="可选：设置默认显示的分支，例如：main、master、develop"
              >
                <template #prefix>
                  <BranchesOutlined />
                </template>
              </a-input>
              <div class="form-item-help">设置构建时默认选中的分支，留空则默认选择仓库的默认分支。实际构建时将使用用户选择的分支进行构建。</div>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="Git Token" name="git_token_id" required>
              <a-select
                v-model:value="formState.git_token_id"
                placeholder="请选择Git Token"
                :loading="gitCredentialsLoading"
                :options="gitCredentials"
                show-search
                :filter-option="filterOption"
              >
                <template #suffixIcon>
                  <ReloadOutlined
                    :spin="gitCredentialsLoading"
                    @click="loadGitCredentials"
                  />
                </template>
              </a-select>
              <div class="form-item-help">
                用于访问Git仓库的Token凭据，如果没有合适的凭据，请先在凭据管理中添加
              </div>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="SSH密钥凭据 (可选)" name="ssh_key_credential_id">
              <a-select
                v-model:value="formState.ssh_key_credential_id"
                placeholder="如果需要在构建脚本中SSH连接远程服务器，请选择密钥凭据"
                :loading="sshKeyCredentialsLoading"
                show-search
                allow-clear
                :filter-option="filterOption"
              >
                <template #suffixIcon>
                  <ReloadOutlined
                    :spin="sshKeyCredentialsLoading"
                    @click="loadSshKeyCredentials"
                  />
                </template>
                <a-select-option
                  v-for="credential in sshKeyCredentials"
                  :key="credential.value" 
                  :value="credential.value"
                  :label="credential.label" 
                >
                  <span>{{ credential.label }}</span>
                  <span style="color: #888; margin-left: 8px;"> ({{ credential.username }})</span>
                </a-select-option>
              </a-select>
              <div class="form-item-help">
                选中的密钥将在构建时自动加载到SSH Agent中，供脚本使用。
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>

      <a-card class="card-wrapper">
        <template #title>
          <div class="card-title-with-action">
            <span>构建配置</span>
            <SystemVariablesList />
          </div>
        </template>
        <div class="stages-list">
          <div v-for="(stage, index) in formState.stages" :key="index" class="stage-item">
            <div class="stage-header">
              <span class="stage-number">
                <BuildOutlined /> 阶段 {{ index + 1 }}
              </span>
              <a-space>
                <a-tooltip title="上移">
                  <a-button
                    v-if="index > 0"
                    type="text"
                    @click="moveStage(index, 'up')"
                    :disabled="index === 0"
                  >
                    <UpOutlined />
                  </a-button>
                </a-tooltip>
                <a-tooltip title="下移">
                  <a-button
                    v-if="index < formState.stages.length - 1"
                    type="text"
                    @click="moveStage(index, 'down')"
                  >
                    <DownOutlined />
                  </a-button>
                </a-tooltip>
                <a-tooltip title="删除">
                  <a-button
                    v-if="formState.stages.length > 1"
                    type="text"
                    danger
                    @click="removeStage(index)"
                  >
                    <DeleteOutlined />
                  </a-button>
                </a-tooltip>
              </a-space>
            </div>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item
                  :name="['stages', index, 'name']"
                  :rules="[{ required: true, message: '请输入阶段名称' }]"
                >
                  <a-input v-model:value="stage.name" placeholder="阶段名称">
                    <template #prefix>
                      <TagOutlined />
                    </template>
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="16">
                <a-form-item
                  :name="['stages', index, 'script_type']"
                  :rules="[{ required: true, message: '请选择脚本类型' }]"
                >
                  <a-radio-group v-model:value="stage.script_type" @change="() => handleScriptTypeChange(index)">
                    <a-radio-button value="inline">
                      <CodeOutlined /> 内联脚本
                    </a-radio-button>
                    <a-radio-button value="file">
                      <FileOutlined /> 脚本文件
                    </a-radio-button>
                  </a-radio-group>
                </a-form-item>

                <template v-if="stage.script_type === 'inline'">
                  <a-form-item
                    :name="['stages', index, 'script']"
                    :rules="[{ required: true, message: '请输入执行脚本' }]"
                  >
                    <CodeEditor
                      v-model="stage.script"
                      :title="`Shell Script - ${stage.name || '未命名阶段'}`"
                      :placeholder="shellScriptPlaceholder"
                      :max-height="400"
                    />
                  </a-form-item>
                </template>

                <template v-else>
                  <a-form-item
                    :name="['stages', index, 'script_file']"
                    :rules="[
                      { required: true, message: '请输入脚本文件路径' },
                      { pattern: /^\/.*\.sh$/, message: '请输入正确的脚本文件路径，必须以/开头，以.sh结尾' }
                    ]"
                  >
                    <a-input
                      v-model:value="stage.script_file"
                      placeholder="请输入脚本文件路径，例如：/scripts/build/deploy.sh"
                    >
                      <template #prefix>
                        <FileOutlined />
                      </template>
                    </a-input>
                  </a-form-item>
                  <div class="script-file-help">
                    <InfoCircleOutlined /> 脚本文件将从项目仓库中读取，请确保文件存在且有执行权限（chmod +x）
                  </div>
                </template>
              </a-col>
            </a-row>
          </div>
        </div>

        <div class="stage-actions">
          <a-button type="dashed" block @click="addStage">
            <PlusOutlined /> 添加构建阶段
          </a-button>
        </div>
      </a-card>

      <!-- 使用新的通知配置组件 -->
      <BuildNotification v-model="formState.notification_channels" />

      <div class="form-footer">
        <a-space>
          <a-button @click="handleBack">取消</a-button>
          <a-button type="primary" :loading="submitLoading" @click="handleSubmit">
            保存
          </a-button>
        </a-space>
      </div>
    </a-form>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import {
  PlusOutlined,
  DeleteOutlined,
  ReloadOutlined,
  QuestionCircleOutlined,
  BuildOutlined,
  DeploymentUnitOutlined,
  BranchesOutlined,
  CodeOutlined,
  FileOutlined,
  LinkOutlined,
  ClockCircleOutlined,
  FieldTimeOutlined,
  TagOutlined,
  UpOutlined,
  DownOutlined,
  ThunderboltOutlined,
  CopyOutlined,
  LockOutlined,
  FolderOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  MailOutlined,
  KeyOutlined,
} from '@ant-design/icons-vue';
import axios from 'axios';
import CodeEditor from './components/CodeEditor.vue';
import BuildNotification from './components/BuildNotification.vue';
import SystemVariablesList from './components/SystemVariablesList.vue';

const router = useRouter();
const route = useRoute();
const formRef = ref();
const isEdit = ref(false);
const submitLoading = ref(false);
const loading = ref(false);
const projectOptions = ref([]);
const environmentOptions = ref([]);
const gitCredentials = ref([]);
const gitCredentialsLoading = ref(false);
const sshKeyCredentials = ref([]);
const sshKeyCredentialsLoading = ref(false);

// 面包屑路由
const routes = [
  {
    path: 'build',
    breadcrumbName: '构建与部署',
  },
  {
    path: 'tasks',
    breadcrumbName: '构建任务',
  },
  {
    path: '',
    breadcrumbName: isEdit.value ? '编辑任务' : '新建任务',
  },
];

// 表单状态
const formState = reactive({
  task_id: '',
  name: '',
  project_id: undefined,
  environment_id: undefined,
  description: '',
  branch: '',
  git_token_id: undefined,
  ssh_key_credential_id: undefined,
  stages: [
    {
      name: '构建',
      script_type: 'inline',
      script: '',
      script_file: '',
    }
  ],
  notification_channels: [],
});

// 表单校验规则
const rules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 2, max: 50, message: '任务名称长度应在 2-50 个字符之间', trigger: 'blur' },
  ],
  project_id: [
    { required: true, message: '请选择项目', trigger: 'change' },
  ],
  environment_id: [
    { required: true, message: '请选择环境', trigger: 'change' },
  ],
  git_token_id: [
    { required: true, message: '请选择Git Token', trigger: 'change' },
  ],
  ssh_key_credential_id: [
    { required: false, message: '请选择SSH密钥凭据', trigger: 'change' },
  ],
};

// 加载项目列表
const loadProjects = async () => {
  try {
    const token = localStorage.getItem('token');
    const response = await axios.get('/api/projects/', {
      headers: { 'Authorization': token }
    });
    
    if (response.data.code === 200) {
      projectOptions.value = response.data.data.map(item => ({
        label: item.name,
        value: item.project_id
      }));
    }
  } catch (error) {
    console.error('Load projects error:', error);
    message.error('加载项目列表失败');
  }
};

// 加载环境列表
const loadEnvironments = async () => {
  try {
    const token = localStorage.getItem('token');
    const response = await axios.get('/api/environments/', {
      headers: { 'Authorization': token }
    });
    
    if (response.data.code === 200) {
      environmentOptions.value = response.data.data.map(item => ({
        label: item.name,
        value: item.environment_id
      }));
    }
  } catch (error) {
    console.error('Load environments error:', error);
    message.error('加载环境列表失败');
  }
};

// 加载Git Token凭据列表
const loadGitCredentials = async () => {
  try {
    gitCredentialsLoading.value = true;
    const token = localStorage.getItem('token');
    const response = await axios.get('/api/credentials/', {
      params: { type: 'gitlab_token' },
      headers: { 'Authorization': token }
    });
    
    if (response.data.code === 200) {
      gitCredentials.value = response.data.data.map(item => ({
        label: item.name,
        value: item.credential_id,
        description: item.description
      }));
    }
  } catch (error) {
    console.error('Load git credentials error:', error);
    message.error('加载Git Token凭据失败');
  } finally {
    gitCredentialsLoading.value = false;
  }
};

// 新增：加载 SSH 密钥凭据列表
const loadSshKeyCredentials = async () => {
  try {
    sshKeyCredentialsLoading.value = true;
    const token = localStorage.getItem('token');
    const response = await axios.get('/api/credentials/', {
      params: { type: 'ssh_key' },
      headers: { 'Authorization': token }
    });
    
    if (response.data.code === 200) {
      sshKeyCredentials.value = response.data.data.map(item => ({
        label: item.name,
        value: item.credential_id,
        description: item.description,
        username: item.username
      }));
    }
  } catch (error) {
    console.error('Load ssh key credentials error:', error);
    message.error('加载SSH密钥凭据失败');
  } finally {
    sshKeyCredentialsLoading.value = false;
  }
};

// 处理项目变更
const handleProjectChange = async (value) => {
  const project = projectOptions.value.find(item => item.value === value);
  if (project) {
    // 可以根据项目信息加载其他相关数据
  }
};

// 添加构建阶段
const addStage = () => {
  formState.stages.push({
    name: '',
    script_type: 'inline',
    script: '',
    script_file: '',
  });
};

// 移除构建阶段
const removeStage = (index) => {
  formState.stages.splice(index, 1);
};

// 处理返回
const handleBack = () => {
  router.back();
};

// 处理提交
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    submitLoading.value = true;
    
    const token = localStorage.getItem('token');
    const method = isEdit.value ? 'put' : 'post';
    
    // 构建提交数据
    const submitData = { ...formState };
    if (!isEdit.value) {
      // 如果是新建，删除task_id字段
      delete submitData.task_id;
    }
    
    const response = await axios[method]('/api/build/tasks/', submitData, {
      headers: { 'Authorization': token }
    });

    if (response.data.code === 200) {
      message.success(`${isEdit.value ? '更新' : '创建'}构建任务成功`);
      router.push('/build/tasks');
    } else {
      throw new Error(response.data.message);
    }
  } catch (error) {
    console.error('Submit task error:', error);
    message.error(error.message || `${isEdit.value ? '更新' : '创建'}构建任务失败`);
  } finally {
    submitLoading.value = false;
  }
};

// 加载任务详情
const loadTaskDetail = async (taskId) => {
  try {
    loading.value = true;
    const token = localStorage.getItem('token');
    const response = await axios.get(`/api/build/tasks/${taskId}`, {
      headers: { 'Authorization': token }
    });
    
    if (response.data.code === 200) {
      // 映射后端返回的嵌套数据到表单状态
      formState.task_id = response.data.data.task_id;
      formState.name = response.data.data.name;
      formState.description = response.data.data.description;
      formState.branch = response.data.data.branch;
      formState.stages = response.data.data.stages || [];
      formState.notification_channels = response.data.data.notification_channels || [];
      
      // 正确映射项目、环境和凭据的ID
      if (response.data.data.project) {
        formState.project_id = response.data.data.project.project_id;
      }
      if (response.data.data.environment) {
        formState.environment_id = response.data.data.environment.environment_id;
      }
      if (response.data.data.git_token) {
        formState.git_token_id = response.data.data.git_token.credential_id;
      }
      if (response.data.data.ssh_key_credential) {
        formState.ssh_key_credential_id = response.data.data.ssh_key_credential.credential_id;
      }

      // 加载相关选项数据
      await Promise.all([
        loadProjects(),
        loadEnvironments(),
        loadGitCredentials(),
        loadSshKeyCredentials()
      ]);
    } else {
      message.error(response.data.message || '加载任务详情失败');
    }
  } catch (error) {
    console.error('加载任务详情失败:', error);
    message.error('加载任务详情失败');
  } finally {
    loading.value = false;
  }
};

// 过滤选项方法
const filterOption = (input, option) => {
  return (
    option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0 ||
    option.description?.toLowerCase().indexOf(input.toLowerCase()) >= 0
  );
};

// 移动构建阶段
const moveStage = (index, direction) => {
  const stages = formState.stages;
  if (direction === 'up' && index > 0) {
    [stages[index], stages[index - 1]] = [stages[index - 1], stages[index]];
  } else if (direction === 'down' && index < stages.length - 1) {
    [stages[index], stages[index + 1]] = [stages[index + 1], stages[index]];
  }
};

// Shell脚本占位符
const shellScriptPlaceholder = `#!/bin/bash
# 在这里输入shell脚本
# 支持所有shell命令和Jenkins环境变量
# 例如：
# echo $JOB_NAME
# echo $BUILD_NUMBER
# echo $WORKSPACE`;

// 处理脚本类型变更
const handleScriptTypeChange = (index) => {
  const stage = formState.stages[index];
  // 切换类型时清空另一种类型的值
  if (stage.script_type === 'inline') {
    stage.script_file = '';
  } else {
    stage.script = '';
  }
};

onMounted(async () => {
  const taskId = route.query.task_id;
  if (taskId) {
    isEdit.value = true;
    await loadTaskDetail(taskId);
  } else {
    loadProjects();
    loadEnvironments();
    loadGitCredentials();
    loadSshKeyCredentials();
  }
});
</script>

<style scoped>
.build-task-edit {
  padding: 24px;
  background: #f0f2f5;
}

.page-header {
  margin-bottom: 24px;
  background: #fff;
  border-radius: 4px;
}

:deep(.ant-page-header) {
  padding: 16px 24px;
}

.card-wrapper {
  margin-bottom: 24px;
  border-radius: 4px;
}

:deep(.ant-card-head) {
  border-bottom: 1px solid #f0f0f0;
  padding: 0 24px;
}

:deep(.ant-card-head-title) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.ant-card-body) {
  padding: 24px;
}

.form-item-help {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  margin-top: 4px;
}

.stages-list {
  margin-top: 16px;
}

.stage-item {
  padding: 16px;
  background: #fafafa;
  border-radius: 4px;
  margin-bottom: 16px;
  border: 1px solid #f0f0f0;
}

.stage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px dashed #f0f0f0;
}

.stage-number {
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.stage-actions {
  margin-top: 16px;
}

.form-footer {
  margin-top: 24px;
  text-align: center;
  background: #fff;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

:deep(.ant-radio-button-wrapper) {
  margin-right: 8px;
}

:deep(.ant-checkbox-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}

ul {
  margin: 0;
  padding-left: 16px;
}

li {
  color: rgba(0, 0, 0, 0.45);
}

.script-file-help {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  margin-top: 8px;
  padding: 8px;
  background-color: #fafafa;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.channel-icon {
  width: 16px;
  height: 16px;
  vertical-align: -0.125em;
}

.notification-template-vars {
  margin-top: 8px;
  padding: 8px;
  background-color: #fafafa;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.card-title-with-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
</style> 