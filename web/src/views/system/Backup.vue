<template>
  <div class="backup">
    <div class="page-header">
      <a-row justify="space-between" align="middle">
        <a-col>
          <h2>备份恢复</h2>
        </a-col>
        <a-col>
          <a-button type="primary" @click="handleManualBackup" :loading="backupLoading">
            <template #icon><CloudUploadOutlined /></template>
            立即备份
          </a-button>
        </a-col>
      </a-row>
    </div>

    <a-card title="备份配置" style="margin-bottom: 24px;">
      <a-form
        :model="backupConfig"
        :rules="rules"
        ref="configFormRef"
        layout="vertical"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="备份目录" name="backup_dir">
              <a-input
                v-model:value="backupConfig.backup_dir"
                placeholder="请输入备份文件保存目录"
                allow-clear
              >
                <template #addonAfter>
                  <a-tooltip title="选择目录">
                    <FolderOpenOutlined @click="handleSelectDirectory" />
                  </a-tooltip>
                </template>
              </a-input>
              <div class="form-item-help">系统将在此目录下创建备份文件，请确保目录存在且有写入权限</div>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="备份文件保留数量" name="max_backups">
              <a-input-number
                v-model:value="backupConfig.max_backups"
                :min="1"
                :max="100"
                style="width: 100%"
                placeholder="请输入备份文件保留数量"
              />
              <div class="form-item-help">超过此数量的旧备份文件将被自动删除</div>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="启用定时备份" name="enable_scheduled_backup">
              <a-switch v-model:checked="backupConfig.enable_scheduled_backup" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24" v-if="backupConfig.enable_scheduled_backup">
          <a-col :span="12">
            <a-form-item label="备份频率" name="backup_frequency">
              <a-select
                v-model:value="backupConfig.backup_frequency"
                placeholder="请选择备份频率"
              >
                <a-select-option value="daily">每天</a-select-option>
                <a-select-option value="weekly">每周</a-select-option>
                <a-select-option value="monthly">每月</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="备份时间" name="backup_time">
              <a-time-picker
                v-model:value="backupConfig.backup_time"
                format="HH:mm"
                style="width: 100%"
                placeholder="请选择备份时间"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24" v-if="backupConfig.enable_scheduled_backup && backupConfig.backup_frequency === 'weekly'">
          <a-col :span="12">
            <a-form-item label="备份日" name="backup_day">
              <a-select
                v-model:value="backupConfig.backup_day"
                placeholder="请选择每周几进行备份"
              >
                <a-select-option value="1">周一</a-select-option>
                <a-select-option value="2">周二</a-select-option>
                <a-select-option value="3">周三</a-select-option>
                <a-select-option value="4">周四</a-select-option>
                <a-select-option value="5">周五</a-select-option>
                <a-select-option value="6">周六</a-select-option>
                <a-select-option value="0">周日</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24" v-if="backupConfig.enable_scheduled_backup && backupConfig.backup_frequency === 'monthly'">
          <a-col :span="12">
            <a-form-item label="备份日期" name="backup_date">
              <a-select
                v-model:value="backupConfig.backup_date"
                placeholder="请选择每月几号进行备份"
              >
                <a-select-option v-for="i in 31" :key="i" :value="i">{{ i }}日</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-item label="备份内容" name="backup_content">
              <a-checkbox-group v-model:value="backupConfig.backup_content">
                <a-checkbox value="database">数据库</a-checkbox>
              </a-checkbox-group>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item>
          <a-button type="primary" @click="saveBackupConfig" :loading="saveConfigLoading">
            保存配置
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <a-card title="备份历史">
      <a-table
        :columns="backupHistoryColumns"
        :data-source="backupHistoryList"
        :loading="historyLoading"
        :pagination="{ pageSize: 10 }"
        row-key="backup_id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'backup_size'">
            {{ formatFileSize(record.backup_size) }}
          </template>
          <template v-else-if="column.key === 'backup_content'">
            <a-tag color="">{{ getDatabaseName(record.backup_name) }}</a-tag>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-popconfirm
                title="确定要恢复到此备份吗？此操作将覆盖当前数据，无法撤销！"
                @confirm="handleRestoreBackup(record)"
              >
                <a-button type="link" size="small">恢复</a-button>
              </a-popconfirm>
              <a-popconfirm
                title="确定要删除此备份吗？"
                @confirm="handleDeleteBackup(record)"
              >
                <a-button type="link" danger size="small">删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import {
  CloudUploadOutlined,
  FolderOpenOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import axios from 'axios';
import dayjs from 'dayjs';
import { hasMenuPermission } from '../../utils/permission';

// 备份配置表单
const configFormRef = ref();
const backupConfig = reactive({
  backup_dir: '',
  max_backups: 10,
  enable_scheduled_backup: false,
  backup_frequency: 'daily',
  backup_time: null,
  backup_day: '1',
  backup_date: 1,
  backup_content: ['database'] // 只保留数据库备份
});

// 表单验证规则
const rules = {
  backup_dir: [
    { required: true, message: '请输入备份目录', trigger: 'blur' },
  ],
  max_backups: [
    { required: true, message: '请输入备份文件保留数量', trigger: 'blur' },
  ],
  backup_frequency: [
    { required: true, message: '请选择备份频率', trigger: 'change' },
  ],
  backup_time: [
    { required: true, message: '请选择备份时间', trigger: 'change' },
  ],
  backup_content: [
    { required: true, message: '请选择备份内容', trigger: 'change' },
  ],
};

// 备份历史表格列定义
const backupHistoryColumns = [
  {
    title: '备份ID',
    dataIndex: 'backup_id',
    key: 'backup_id',
    width: 120,
  },
  {
    title: '备份名称',
    dataIndex: 'backup_name',
    key: 'backup_name',
    width: 250,
  },
  {
    title: '备份内容',
    dataIndex: 'backup_content',
    key: 'backup_content',
    width: 120,
  },
  {
    title: '备份大小',
    dataIndex: 'backup_size',
    key: 'backup_size',
    width: 120,
  },
  {
    title: '备份时间',
    dataIndex: 'create_time',
    key: 'create_time',
    width: 200,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
  },
];

// 状态变量
const saveConfigLoading = ref(false);
const backupLoading = ref(false);
const historyLoading = ref(false);
const backupHistoryList = ref([]);

// 加载备份配置
const loadBackupConfig = async () => {
  try {
    // 检查菜单权限
    if (!hasMenuPermission('/system/backup')) {
      return;
    }

    const token = localStorage.getItem('token');
    const response = await axios.get('/api/system/backup/config/', {
      headers: { 'Authorization': token }
    });

    if (response.data.code === 200) {
      const config = response.data.data;

      // 更新表单数据
      Object.assign(backupConfig, {
        backup_dir: config.backup_dir || '',
        max_backups: config.max_backups || 10,
        enable_scheduled_backup: config.enable_scheduled_backup || false,
        backup_frequency: config.backup_frequency || 'daily',
        backup_time: config.backup_time ? dayjs(config.backup_time, 'HH:mm') : null,
        backup_day: config.backup_day || '1',
        backup_date: config.backup_date || 1,
        backup_content: config.backup_content || ['database']
      });
    } else {
      message.error(response.data.message || '获取备份配置失败');
    }
  } catch (error) {
    console.error('Load backup config error:', error);
    message.error('获取备份配置失败');
  }
};

// 保存备份配置
const saveBackupConfig = async () => {
  try {
    // 检查菜单权限
    if (!hasMenuPermission('/system/backup')) {
      return;
    }

    await configFormRef.value.validate();
    saveConfigLoading.value = true;

    // 格式化时间
    const formattedConfig = {
      ...backupConfig,
      backup_time: backupConfig.backup_time ? backupConfig.backup_time.format('HH:mm') : null,
    };

    const token = localStorage.getItem('token');
    const response = await axios.post('/api/system/backup/config/', formattedConfig, {
      headers: { 'Authorization': token }
    });

    if (response.data.code === 200) {
      message.success('保存备份配置成功');
    } else {
      message.error(response.data.message || '保存备份配置失败');
    }
  } catch (error) {
    console.error('Save backup config error:', error);
    message.error('保存备份配置失败');
  } finally {
    saveConfigLoading.value = false;
  }
};

// 加载备份历史
const loadBackupHistory = async () => {
  try {
    // 检查菜单权限
    if (!hasMenuPermission('/system/backup')) {
      return;
    }

    historyLoading.value = true;
    const token = localStorage.getItem('token');
    const response = await axios.get('/api/system/backup/history/', {
      headers: { 'Authorization': token }
    });

    if (response.data.code === 200) {
      backupHistoryList.value = response.data.data;
    } else {
      message.error(response.data.message || '获取备份历史失败');
    }
  } catch (error) {
    console.error('Load backup history error:', error);
    message.error('获取备份历史失败');
  } finally {
    historyLoading.value = false;
  }
};

// 手动备份
const handleManualBackup = async () => {
  try {
    // 检查菜单权限
    if (!hasMenuPermission('/system/backup')) {
      return;
    }

    backupLoading.value = true;
    const token = localStorage.getItem('token');
    const response = await axios.post('/api/system/backup/create/', {
      backup_content: backupConfig.backup_content,
      backup_dir: backupConfig.backup_dir,
      backup_type: 'manual'
    }, {
      headers: { 'Authorization': token }
    });

    if (response.data.code === 200) {
      message.success('备份创建成功');
      loadBackupHistory();  // 重新加载备份历史
    } else {
      message.error(response.data.message || '创建备份失败');
    }
  } catch (error) {
    console.error('Create backup error:', error);
    message.error('创建备份失败');
  } finally {
    backupLoading.value = false;
  }
};

// 恢复备份
const handleRestoreBackup = async (record) => {
  try {
    // 检查菜单权限
    if (!hasMenuPermission('/system/backup')) {
      return;
    }

    const token = localStorage.getItem('token');
    const response = await axios.post('/api/system/backup/restore/', {
      backup_id: record.backup_id
    }, {
      headers: { 'Authorization': token }
    });

    if (response.data.code === 200) {
      message.success('备份恢复成功，系统将在5秒后重启');
    } else {
      message.error(response.data.message || '恢复备份失败');
    }
  } catch (error) {
    console.error('Restore backup error:', error);
    message.error('恢复备份失败');
  }
};

// 删除备份
const handleDeleteBackup = async (record) => {
  try {
    // 检查菜单权限
    if (!hasMenuPermission('/system/backup')) {
      return;
    }

    const token = localStorage.getItem('token');
    const response = await axios.delete('/api/system/backup/history/', {
      headers: { 'Authorization': token },
      data: { backup_id: record.backup_id }
    });

    if (response.data.code === 200) {
      message.success('删除备份成功');
      loadBackupHistory();  // 重新加载备份历史
    } else {
      message.error(response.data.message || '删除备份失败');
    }
  } catch (error) {
    console.error('Delete backup error:', error);
    message.error('删除备份失败');
  }
};

// 选择目录
const handleSelectDirectory = () => {
  message.info('请输入备份目录');
};

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '0 B';

  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let i = 0;
  while (size >= 1024 && i < units.length - 1) {
    size /= 1024;
    i++;
  }

  return `${size.toFixed(2)} ${units[i]}`;
};

// 从备份文件名中提取数据库名称
const getDatabaseName = (backupName) => {
  if (!backupName) return '数据库';

  // 备份文件名格式: backup_YYYYMMDD_HHMMSS_dbname.sql.gz
  const parts = backupName.split('_');
  if (parts.length >= 4) {
    // 提取数据库名称，去掉.sql.gz后缀
    const dbNameWithExt = parts[3];
    return dbNameWithExt.replace(/\.sql\.gz$/, '');
  }

  return '数据库';
};

// 生命周期钩子
onMounted(() => {
  loadBackupConfig();
  loadBackupHistory();
});
</script>

<style scoped>
.backup {
  padding-bottom: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.form-item-help {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  margin-top: 4px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}
</style>
