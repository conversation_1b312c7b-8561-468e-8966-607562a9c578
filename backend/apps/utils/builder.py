import os
import logging
import time
import subprocess
import tempfile
import re
from datetime import datetime
from pathlib import Path
from django.conf import settings
from git import Repo
from git.exc import GitCommandError
from .build_stages import BuildStageExecutor
from .notifier import BuildNotifier
from .log_stream import log_stream_manager
from django.db.models import F
from ..models import BuildTask, BuildHistory, SSHKeyCredential
# from ..utils.builder import Builder
# from ..utils.crypto import decrypt_sensitive_data

logger = logging.getLogger('apps')

class Builder:
    def __init__(self, task, build_number, commit_id, history):
        self.task = task
        self.build_number = build_number
        self.commit_id = commit_id
        self.history = history  # 构建历史记录
        self.log_buffer = []  # 用于缓存日志
        self.ssh_agent_env = {} # <-- 用于存储 SSH Agent 的环境变量
        self.ssh_credential = None # <-- 用于存储 SSH 凭据对象

        # 检查是否已有指定的版本号（预发布和生产环境）
        if self.history.version:
            # 如果已经有版本号（来自预发布和生产环境的请求），直接使用它
            self.version = self.history.version
            self.send_log(f"使用指定版本: {self.version}", "Version")
        else:
            # 为开发和测试环境生成新的版本号
            self.version = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{commit_id[:8]}"

        # 初始化构建时间信息
        self.build_time = {
            'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'stages_time': []
        }

        # 更新构建历史记录的状态和版本
        self.history.status = 'running'
        if not self.history.version:  # 只有当没有版本号时才更新
            self.history.version = self.version
        self.history.build_time = self.build_time
        self.history.save(update_fields=['status', 'version', 'build_time'])

        # 设置构建目录
        self.build_path = Path(settings.BUILD_ROOT) / task.name / self.version / task.project.name
        
        # 创建实时日志流
        log_stream_manager.create_build_stream(self.task.task_id, self.build_number)

    def check_if_terminated(self):
        """检查构建是否已被终止"""
        # 从数据库重新加载构建历史记录，以获取最新状态
        try:
            history_record = BuildHistory.objects.get(history_id=self.history.history_id)
            if history_record.status == 'terminated':
                # 如果状态为terminated，说明构建已被手动终止
                self.send_log("检测到构建已被手动终止，停止后续步骤", "System")
                return True
            return False
        except Exception as e:
            logger.error(f"检查构建状态时出错: {str(e)}", exc_info=True)
            return False

    def _filter_maven_progress(self, message):
        """过滤Maven构建中的Progress信息，让输出更像终端执行的结果"""
        # 只过滤Maven下载进度信息中的Progress()部分
        if 'Progress (' in message and ('KB' in message or 'MB' in message or 'B/s' in message):
            return None
        
        # 过滤Maven下载进度条
        if re.match(r'^Progress \(\d+\): .+', message.strip()):
            return None
            
        # 过滤空的进度行
        if re.match(r'^\s*Progress\s*$', message.strip()):
            return None
            
        # 过滤下载进度百分比
        if re.match(r'^\s*\d+%\s*$', message.strip()):
            return None
            
        return message

    def send_log(self, message, stage=None, console_only=False, raw_output=False):
        """发送日志到缓存、实时流和控制台
        Args:
            message: 日志消息
            stage: 阶段名称
            console_only: 是否只输出到控制台（保留参数兼容性）
            raw_output: 是否为原始输出（不添加阶段标记）
        """
        # 过滤Maven Progress信息
        filtered_message = self._filter_maven_progress(message)
        if filtered_message is None:
            return  # 跳过被过滤的消息
        
        # 格式化消息
        if raw_output:
            formatted_message = filtered_message
        else:
            # 简单格式化，不添加颜色
            formatted_message = filtered_message

            # 如果有阶段名称，添加阶段标记
            if stage:
                formatted_message = f"[{stage}] {filtered_message}"

        # 缓存日志
        self.log_buffer.append(formatted_message)

        # 立即推送到实时日志流
        try:
            log_stream_manager.push_log(
                task_id=self.task.task_id,
                build_number=self.build_number,
                message=formatted_message + '\n',
                stage=stage
            )
        except Exception as e:
            logger.error(f"推送实时日志失败: {str(e)}", exc_info=True)

        # 批量更新数据库中的构建日志（每10条或每5秒更新一次）
        try:
            should_update_db = (
                len(self.log_buffer) % 10 == 0 or  # 每10条日志更新一次
                not hasattr(self, '_last_db_update') or
                time.time() - getattr(self, '_last_db_update', 0) >= 5  # 每5秒更新一次
            )
            
            if should_update_db:
                current_log = '\n'.join(self.log_buffer)
                self.history.build_log = current_log
                self.history.save(update_fields=['build_log'])
                self._last_db_update = time.time()
        except Exception as e:
            logger.error(f"批量更新构建日志失败: {str(e)}", exc_info=True)

        # 输出到控制台 - 确保构建日志在控制台显示
        logger.info(formatted_message, extra={
            'from_builder': True,  # 添加标记以区分构建日志
            'task_id': self.task.task_id,
            'build_number': self.build_number
        })

    def _save_build_log(self):
        """保存构建日志到历史记录"""
        try:
            self.history.build_log = '\n'.join(self.log_buffer)
            self.history.save(update_fields=['build_log'])
        except Exception as e:
            logger.error(f"保存构建日志失败: {str(e)}", exc_info=True)

    def _start_ssh_agent(self): # <-- 新增：启动 SSH Agent
        """启动 SSH Agent 并加载密钥"""
        if not self.task.ssh_key_credential_id:
            self.send_log("未配置 SSH 密钥凭据，跳过启动 SSH Agent", "SSH Agent")
            return True # 没有配置也算成功

        try:
            self.send_log("正在启动 SSH Agent...", "SSH Agent")
            # 从数据库获取 SSH 凭据 (在 __init__ 中预加载可能更好，避免重复查询)
            # 注意：这里应该只在需要时才查询，或者在 execute 方法开始时查询一次
            if not self.ssh_credential:
                self.ssh_credential = SSHKeyCredential.objects.get(credential_id=self.task.ssh_key_credential_id)

            # ！！！警告：以下是明文访问，极不安全，必须替换为解密！！！
            private_key = self.ssh_credential.private_key
            passphrase = self.ssh_credential.passphrase
            username = self.ssh_credential.username
            # private_key = decrypt_sensitive_data(self.ssh_credential.encrypted_private_key)
            # passphrase = decrypt_sensitive_data(self.ssh_credential.encrypted_passphrase)

            if not private_key:
                self.send_log("SSH 私钥内容为空！", "SSH Agent")
                return False

            # 1. 启动 ssh-agent
            agent_proc = subprocess.run(['ssh-agent', '-s'], capture_output=True, text=True, check=True)
            agent_output = agent_proc.stdout

            # 解析 ssh-agent 的输出以获取环境变量
            # 输出通常是类似这样的格式：
            # SSH_AUTH_SOCK=/tmp/ssh-XXXXXX/agent.pid; export SSH_AUTH_SOCK;
            # SSH_AGENT_PID=12345; export SSH_AGENT_PID;
            # echo Agent pid 12345;
            import re
            sock_match = re.search(r'SSH_AUTH_SOCK=([^;]+); export SSH_AUTH_SOCK;', agent_output)
            pid_match = re.search(r'SSH_AGENT_PID=(\d+); export SSH_AGENT_PID;', agent_output)

            if not sock_match or not pid_match:
                self.send_log(f"无法从 ssh-agent 输出中解析环境变量: {agent_output}", "SSH Agent")
                return False

            self.ssh_agent_env = {
                'SSH_AUTH_SOCK': sock_match.group(1),
                'SSH_AGENT_PID': pid_match.group(1)
            }
            self.send_log(f"SSH Agent 已启动 (PID: {self.ssh_agent_env['SSH_AGENT_PID']})", "SSH Agent")
            self.send_log(f"SSH Auth Socket: {self.ssh_agent_env['SSH_AUTH_SOCK']}", "SSH Agent")

            # 2. 将私钥添加到 agent
            self.send_log("正在将 SSH 私钥添加到 Agent...", "SSH Agent")
            # 使用 ssh-add 命令通过标准输入添加密钥
            add_cmd = ['ssh-add', '-']
            # 如果有密码，需要处理密码输入。这里使用 expect 可能更健壮，但较复杂。
            # 一个简单但不完美的方法是使用 SSH_ASKPASS，但这需要在环境中准备一个脚本。
            # 另一个选择是尝试直接通过 Popen 的 communicate 输入密码，但这取决于 ssh-add 的行为。
            # 为了简化，这里暂时不处理带密码的密钥，或者假设 ssh-add 会提示输入。
            # 更安全的做法是使用支持密码的库或 expect。
            if passphrase:
                self.send_log("警告：暂未实现自动处理带密码的 SSH 私钥加载。", "SSH Agent")
                # 这里可以尝试不同的策略，例如设置 SSH_ASKPASS 或使用 pexpect
                # return False # 暂时标记为失败，直到实现密码处理

            add_proc = subprocess.Popen(
                add_cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env={**os.environ, **self.ssh_agent_env} # 传递 agent 环境变量给 ssh-add
            )
            stdout, stderr = add_proc.communicate(input=private_key)

            if add_proc.returncode != 0:
                self.send_log(f"添加到 SSH Agent 失败: {stderr}", "SSH Agent")
                # 尝试停止 agent
                self._stop_ssh_agent()
                return False
            else:
                self.send_log(f"SSH 私钥已成功添加到 Agent: {stdout}", "SSH Agent")
                return True

        except FileNotFoundError:
             self.send_log("错误：找不到 ssh-agent 或 ssh-add 命令。请确保 OpenSSH 客户端已安装在构建环境中。", "SSH Agent")
             return False
        except subprocess.CalledProcessError as e:
            self.send_log(f"执行 SSH Agent 相关命令失败: {e.stderr}", "SSH Agent")
            return False
        except SSHKeyCredential.DoesNotExist:
            self.send_log("错误：关联的 SSH 密钥凭据不存在。", "SSH Agent")
            return False
        except Exception as e:
            self.send_log(f"启动或操作 SSH Agent 时发生未知错误: {str(e)}", "SSH Agent")
            # 尝试停止 agent (如果已部分启动)
            self._stop_ssh_agent()
            return False

    def _stop_ssh_agent(self): # <-- 新增：停止 SSH Agent
        """停止 SSH Agent"""
        if not self.ssh_agent_env.get('SSH_AGENT_PID'):
            # self.send_log("SSH Agent 未运行或未记录 PID，无需停止", "Cleanup")
            return

        try:
            self.send_log("正在停止 SSH Agent...", "Cleanup")
            # 使用 ssh-agent -k 命令停止，需要正确的环境变量
            agent_kill_cmd = "ssh-agent -k"
            kill_proc = subprocess.run(
                ['/bin/bash', '-c', agent_kill_cmd],
                env={**os.environ, **self.ssh_agent_env}, # 传递 agent 环境变量
                capture_output=True,
                text=True,
                check=False # 不抛出异常，即使失败也要记录
            )
            # 记录关闭命令的输出
            if kill_proc.stdout:
                self.send_log(f"SSH Agent 关闭输出:\n{kill_proc.stdout.strip()}", "Cleanup", raw_output=True)
            if kill_proc.stderr:
                self.send_log(f"SSH Agent 关闭错误:\n{kill_proc.stderr.strip()}", "Cleanup", raw_output=True)
            if kill_proc.returncode == 0:
                 self.send_log("SSH Agent 已成功停止。", "Cleanup")
            else:
                 self.send_log("停止 SSH Agent 命令执行完成，但可能未成功关闭 (请检查输出)。", "Cleanup")

        except FileNotFoundError:
            self.send_log("错误：找不到 ssh-agent 命令来停止 Agent。", "Cleanup")
        except Exception as agent_kill_error:
            self.send_log(f"停止 SSH Agent 时发生错误: {str(agent_kill_error)}", "Cleanup")
        finally:
            # 无论成功与否，都清除环境变量记录
            self.ssh_agent_env = {}

    def clone_repository(self):
        """克隆Git仓库"""
        try:
            # 检查构建是否已被终止
            if self.check_if_terminated():
                return False

            self.send_log("开始克隆代码...", "Git Clone")
            self.send_log(f"构建目录: {self.build_path}", "Git Clone")

            # 确保目录存在
            self.build_path.parent.mkdir(parents=True, exist_ok=True)

            # 获取Git凭据
            repository = self.task.project.repository
            git_token = self.task.git_token.token if self.task.git_token else None

            # 处理带有token的仓库URL
            if git_token and repository.startswith('http'):
                if '@' in repository:
                    repository = repository.split('@')[1]
                    repository = f'https://oauth2:{git_token}@{repository}'
                else:
                    repository = repository.replace('://', f'://oauth2:{git_token}@')

            # 使用构建历史记录中的分支
            branch = self.history.branch
            self.send_log(f"克隆分支: {branch}", "Git Clone")
            self.send_log("正在克隆代码，请稍候...", "Git Clone")

            # 克隆指定分支的代码
            Repo.clone_from(
                repository,
                str(self.build_path),
                branch=branch,
                progress=self.git_progress
            )

            # 再次检查构建是否已被终止
            if self.check_if_terminated():
                return False

            self.send_log("代码克隆完成", "Git Clone")
            return True

        except GitCommandError as e:
            self.send_log(f"克隆代码失败: {str(e)}", "Git Clone")
            return False
        except Exception as e:
            self.send_log(f"发生错误: {str(e)}", "Git Clone")
            return False

    def git_progress(self, op_code, cur_count, max_count=None, message=''):
        """Git进度回调"""
        # 每秒检查一次构建是否已被终止
        if int(time.time()) % 5 == 0:  # 每5秒检查一次
            if self.check_if_terminated():
                # 如果构建已被终止，尝试引发异常停止Git克隆
                raise Exception("Build terminated")
        pass

    def execute_stages(self, stage_executor):
        """执行构建阶段"""
        try:
            if not self.task.stages:
                self.send_log("没有配置构建阶段", "Build Stages")
                return False

            # 检查构建是否已被终止
            if self.check_if_terminated():
                return False

            # 执行所有阶段
            success = stage_executor.execute_stages(self.task.stages, check_termination=self.check_if_terminated)
            return success

        except Exception as e:
            self.send_log(f"执行构建阶段时发生错误: {str(e)}", "Build Stages")
            return False

    def execute(self):
        """执行构建"""
        build_start_time = time.time()
        success = False # 初始化成功状态
        try:
            # 在开始构建前检查构建是否已被终止
            if self.check_if_terminated():
                self._update_build_stats(False)
                self._save_build_log()
                return False

            # 新增：启动 SSH Agent (如果配置了)
            if not self._start_ssh_agent():
                self.send_log("SSH Agent 启动或加载密钥失败，终止构建。", "SSH Agent")
                self._update_build_stats(False)
                self._update_build_time(build_start_time, False)
                return False # Agent 启动失败，直接返回

            # 获取环境类型
            environment_type = self.task.environment.type if self.task.environment else None

            # 根据环境类型决定是否需要克隆代码
            if environment_type in ['development', 'testing']:
                # 只在开发和测试环境克隆代码
                clone_start_time = time.time()
                if not self.clone_repository():
                    self._update_build_stats(False)  # 更新失败统计
                    self._update_build_time(build_start_time, False)
                    # 发送构建失败通知
                    notifier = BuildNotifier(self.history)
                    notifier.send_notifications()
                    return False

                # 记录代码克隆阶段的时间
                self.build_time['stages_time'].append({
                    'name': 'Git Clone',
                    'start_time': datetime.fromtimestamp(clone_start_time).strftime('%Y-%m-%d %H:%M:%S'),
                    'duration': str(int(time.time() - clone_start_time))
                })
            else:
                # 预发布和生产环境不需要克隆代码
                self.send_log(f"预发布/生产环境构建，跳过代码克隆，直接使用版本: {self.version}", "Environment")

                # 创建构建目录
                os.makedirs(self.build_path, exist_ok=True)

            # 再次检查构建是否已被终止
            if self.check_if_terminated():
                self._update_build_stats(False)
                self._update_build_time(build_start_time, False)
                # 不需要在这里停止 agent，finally 会处理
                return False

            # 创建阶段执行器，传递send_log方法和构建时间记录回调
            stage_executor = BuildStageExecutor(
                str(self.build_path),
                lambda msg, stage=None, raw_output=False: self.send_log(msg, stage, raw_output=raw_output),
                self._record_stage_time  # 传递记录阶段时间的回调函数
            )

            # 设置系统内置环境变量
            system_variables = {
                # 编号相关变量
                'BUILD_NUMBER': str(self.build_number),
                'VERSION': self.version,

                # Git相关变量
                'COMMIT_ID': self.commit_id,
                'BRANCH': self.history.branch,  # 使用构建时选择的分支

                # 项目相关变量
                'PROJECT_NAME': self.task.project.name,
                'PROJECT_ID': self.task.project.project_id,
                'PROJECT_REPO': self.task.project.repository,

                # 任务相关变量
                'TASK_NAME': self.task.name,
                'TASK_ID': self.task.task_id,

                # 环境相关变量
                'ENVIRONMENT': self.task.environment.name,
                'ENVIRONMENT_TYPE': self.task.environment.type,
                'ENVIRONMENT_ID': self.task.environment.environment_id,

                # 别名(便于使用)
                'service_name': self.task.name,
                'build_env': self.task.environment.name,
                'branch': self.history.branch,
                'version': self.version,

                # 构建路径
                'BUILD_PATH': str(self.build_path),
                'BUILD_WORKSPACE': str(self.build_path),
            }

            # !!! 合并 Agent 环境变量到传递给 Executor 的环境 !!!
            combined_env = {**os.environ, **system_variables, **self.ssh_agent_env}

            # 更新环境变量 (Executor 内部会使用自己的 env 副本，并基于此更新)
            # stage_executor.env.update(system_variables)
            # stage_executor.env.update(self.ssh_agent_env) # 传递 Agent 环境变量
            stage_executor.env = combined_env # 直接设置完整的环境

            # 将系统变量保存到变量文件
            # stage_executor._save_variables_to_file(system_variables)
            # 注意：Agent 变量不适合写入 build_vars 文件
            vars_to_save = {k: v for k, v in system_variables.items()
                            if k not in ['SSH_AUTH_SOCK', 'SSH_AGENT_PID']} # 过滤掉 agent 变量
            stage_executor._save_variables_to_file(vars_to_save)

            # 执行构建阶段
            success = self.execute_stages(stage_executor)

            # 构建完成，后续会在 finally 中停止 agent
            return success # 返回执行结果

        except Exception as e:
            self.send_log(f"构建过程中发生未捕获的异常: {str(e)}", "Error")
            success = False # 标记为失败
            return False # 返回失败
        finally:
            # !!! 无论成功、失败或异常，都执行清理 !!!
            # 更新构建统计和时间信息
            self._update_build_stats(success) # 使用 try 块最后得到的 success 状态
            self._update_build_time(build_start_time, success)

            # 确保最终日志保存到数据库
            self._save_build_log()

            # 通知日志流管理器构建完成
            try:
                # 获取最终状态
                self.history.refresh_from_db()
                final_status = self.history.status
                log_stream_manager.complete_build(
                    task_id=self.task.task_id,
                    build_number=self.build_number,
                    status=final_status
                )
            except Exception as e:
                logger.error(f"通知日志流管理器构建完成失败: {str(e)}", exc_info=True)

            # !!! 停止 SSH Agent !!!
            self._stop_ssh_agent()

            # 发送构建通知 (确保在所有清理之后发送)
            notifier = BuildNotifier(self.history)
            notifier.send_notifications()

    def _record_stage_time(self, stage_name: str, start_time: float, duration: float):
        """记录阶段执行时间
        Args:
            stage_name: 阶段名称
            start_time: 开始时间戳
            duration: 耗时（秒）
        """
        stage_time = {
            'name': stage_name,
            'start_time': datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S'),
            'duration': str(int(duration))
        }
        self.build_time['stages_time'].append(stage_time)

        # 更新构建历史记录的阶段信息
        self.history.stages = self.task.stages
        self.history.save(update_fields=['stages'])

    def _update_build_time(self, build_start_time: float, success: bool):
        """更新构建时间信息
        Args:
            build_start_time: 构建开始时间戳
            success: 构建是否成功
        """
        try:
            # 计算总耗时
            total_duration = int(time.time() - build_start_time)

            # 更新构建时间信息
            self.build_time['total_duration'] = str(total_duration)
            self.build_time['end_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 检查当前构建状态，如果已经是terminated则不覆盖状态
            self.history.refresh_from_db()
            if self.history.status != 'terminated':
                # 只有在状态不是terminated时才更新状态
                self.history.status = 'success' if success else 'failed'
            
            self.history.build_time = self.build_time
            self.history.save(update_fields=['status', 'build_time'])

            # 输出构建完成信息
            if self.history.status == 'terminated':
                self.send_log(f"构建已终止，总耗时: {total_duration}秒", "Build")
            elif success:
                self.send_log(f"构建完成，总耗时: {total_duration}秒", "Build")
            else:
                self.send_log(f"构建失败，总耗时: {total_duration}秒", "Build")
        except Exception as e:
            logger.error(f"更新构建时间信息失败: {str(e)}", exc_info=True)

    def _update_build_stats(self, success: bool):
        """更新构建统计信息
        Args:
            success: 构建是否成功
        """
        try:
            # 检查当前构建状态，如果是terminated则不更新统计
            self.history.refresh_from_db()
            if self.history.status == 'terminated':
                # terminated状态不计入成功或失败统计
                return
            
            # 更新任务的构建统计信息
            if success:
                BuildTask.objects.filter(task_id=self.task.task_id).update(
                    success_builds=F('success_builds') + 1
                )
                # 只有成功的构建才更新版本号
                BuildTask.objects.filter(task_id=self.task.task_id).update(
                    version=self.version
                )
            else:
                BuildTask.objects.filter(task_id=self.task.task_id).update(
                    failure_builds=F('failure_builds') + 1
                )
        except Exception as e:
            logger.error(f"更新构建统计信息失败: {str(e)}", exc_info=True)