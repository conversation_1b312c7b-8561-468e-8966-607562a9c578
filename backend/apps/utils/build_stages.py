import os
import subprocess
import logging
import time
import re
from typing import List, Dict, Any, Callable

logger = logging.getLogger('apps')

class BuildStageExecutor:
    """构建阶段执行器"""

    def __init__(self, build_path: str, send_log: Callable, record_time: Callable):
        """
        初始化构建阶段执行器
        Args:
            build_path: 构建目录路径
            send_log: 发送日志的回调函数
            record_time: 记录时间的回调函数
        """
        self.build_path = build_path
        self.send_log = send_log
        self.record_time = record_time
        # self.env = os.environ.copy() # 不再从 os.environ 初始化，由 Builder 提供完整的 env
        self.env = {} # 初始化为空字典，将由 Builder 设置

        # 添加构建相关的环境变量 (这些也可以由 Builder 设置，但在这里保留作为默认值)
        # self.env.update({
        #     'BUILD_PATH': self.build_path,
        #     'BUILD_WORKSPACE': self.build_path,
        # })

        # 用于存储临时变量文件的路径
        self.vars_file = os.path.join(self.build_path, '.build_vars')
        # 初始化变量文件
        self._init_vars_file()

    def _init_vars_file(self):
        """初始化变量文件"""
        try:
            # 创建一个空的变量文件
            with open(self.vars_file, 'w') as f:
                f.write('#!/bin/bash\n# 构建变量\n')
            # 设置执行权限
            os.chmod(self.vars_file, 0o755)
        except Exception as e:
            logger.error(f"初始化变量文件失败: {str(e)}", exc_info=True)

    def _update_env_from_vars_file(self):
        """从变量文件更新环境变量"""
        try:
            if os.path.exists(self.vars_file):
                # 使用source命令执行变量文件并输出所有环境变量
                # 注意：这里更新的是 self.env，确保 Builder 传递的环境变量也被包括
                cmd = f"source {self.vars_file} 2>/dev/null && env"
                process = subprocess.Popen(
                    ['/bin/bash', '-c', cmd],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    env=self.env, # <-- 使用当前的 self.env 来执行
                    universal_newlines=True
                )
                stdout, _ = process.communicate()  # 忽略stderr

                if process.returncode == 0 and stdout:
                    # 解析环境变量
                    for line in stdout.splitlines():
                        if '=' in line:
                            key, value = line.split('=', 1)
                            # 只更新我们关心的变量，忽略系统变量
                            self.env[key] = value
        except Exception as e:
            logger.error(f"从变量文件更新环境变量失败: {str(e)}", exc_info=True)

    def _capture_variables(self, script_content):
        """
        捕获脚本中设置的变量
        Args:
            script_content: 脚本内容
        """
        # 提取变量定义（支持多种格式: var=value, export var=value, var="value"等）
        variable_patterns = [
            r'^([a-zA-Z0-9_]+)="?([^"]*)"?$',  # 匹配 VAR=value 或 VAR="value"
            r'^([a-zA-Z0-9_]+)=\'([^\']*)\'$',  # 匹配 VAR='value'
            r'^export\s+([a-zA-Z0-9_]+)="?([^"]*)"?$',  # 匹配 export VAR=value 或 export VAR="value"
            r'^export\s+([a-zA-Z0-9_]+)=\'([^\']*)\'$',  # 匹配 export VAR='value'
        ]

        variables = {}
        for line in script_content.splitlines():
            line = line.strip()
            if not line or line.startswith('#'):
                continue

            for pattern in variable_patterns:
                match = re.match(pattern, line)
                if match:
                    var_name, var_value = match.groups()
                    variables[var_name] = var_value
                    break

        return variables

    def _save_variables_to_file(self, variables):
        """
        将变量保存到文件
        Args:
            variables: 变量字典
        """
        try:
            if not variables:
                return

            # 追加变量到变量文件
            with open(self.vars_file, 'a') as f:
                for name, value in variables.items():
                    # 跳过系统保留变量和环境变量
                    if name.startswith(('_', 'BASH_', 'SHELL', 'HOME', 'PATH', 'PWD', 'OLDPWD')):
                        continue
                    f.write(f'export {name}="{value}"\n')
        except Exception as e:
            logger.error(f"保存变量到文件失败: {str(e)}", exc_info=True)

    def execute_stage(self, stage: Dict[str, Any], check_termination: Callable = None) -> bool:
        """
        执行单个构建阶段
        Args:
            stage: 阶段配置信息
            check_termination: 检查是否终止的回调函数
        Returns:
            bool: 执行是否成功
        """
        try:
            stage_name = stage.get('name', '未命名阶段')
            script_type = stage.get('script_type')

            # 检查是否应该终止
            if check_termination and check_termination():
                self.send_log("构建已被终止，跳过此阶段", stage_name)
                return False

            # 从变量文件更新环境变量
            self._update_env_from_vars_file()

            # 记录阶段开始时间
            stage_start_time = time.time()

            # 执行阶段
            success = False
            if script_type == 'inline':
                success = self._execute_inline_script(stage, check_termination)
            elif script_type == 'file':
                success = self._execute_script_file(stage, check_termination)
            else:
                self.send_log(f"不支持的脚本类型: {script_type}", stage_name)

            # 记录阶段耗时
            stage_duration = time.time() - stage_start_time
            self.record_time(stage_name, stage_start_time, stage_duration)

            return success

        except Exception as e:
            self.send_log(f"执行阶段时发生错误: {str(e)}", stage_name)
            return False

    def _execute_command(self, command: str, stage_name: str, show_command: bool = True, capture_vars: bool = True, check_termination: Callable = None) -> bool:
        """
        执行单个命令
        Args:
            command: 要执行的命令
            stage_name: 阶段名称
            show_command: 是否显示要执行的命令
            capture_vars: 是否捕获命令中的变量
            check_termination: 检查是否终止的回调函数
        Returns:
            bool: 执行是否成功
        """
        try:
            # 检查是否应该终止
            if check_termination and check_termination():
                self.send_log("构建已被终止，跳过此命令", stage_name)
                return False

            # 显示要执行的命令
            if show_command:
                self.send_log(f"> {command}", stage_name)

            # 如果命令是设置变量，捕获变量
            if capture_vars:
                variables = self._capture_variables(command)
                if variables:
                    # 将变量添加到环境变量
                    self.env.update(variables)
                    # 保存变量到文件
                    self._save_variables_to_file(variables)

            # 构建包含变量文件的完整命令
            # 确保命令在包含正确环境变量（包括 SSH Agent 变量）的 shell 中执行
            full_command = f"source {self.vars_file} 2>/dev/null || true; {command}" # 使用分号确保 source 先执行

            # 执行命令，使用实时流式输出
            process = subprocess.Popen(
                ['/bin/bash', '-c', full_command],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=self.build_path,
                env=self.env, # <-- 关键：传递包含 Agent 变量的 env
                universal_newlines=True,
                bufsize=1  # 行缓冲，确保输出能够实时获取
            )

            # 实时读取并发送输出
            stdout_lines = []
            stderr_lines = []

            # 非阻塞读取输出
            import select
            import fcntl
            import os

            # 设置非阻塞模式
            for pipe in [process.stdout, process.stderr]:
                fd = pipe.fileno()
                fl = fcntl.fcntl(fd, fcntl.F_GETFL)
                fcntl.fcntl(fd, fcntl.F_SETFL, fl | os.O_NONBLOCK)

            # 持续读取直到进程结束
            while process.poll() is None:
                # 检查是否应该终止
                if check_termination and check_termination():
                    process.terminate()
                    self.send_log("构建已被终止，停止当前命令", stage_name)
                    return False

                # 使用select监控管道是否有数据可读
                readable, _, _ = select.select([process.stdout, process.stderr], [], [], 0.1)

                for pipe in readable:
                    line = pipe.readline()
                    if not line:
                        continue

                    line = line.rstrip()
                    if pipe == process.stdout:
                        stdout_lines.append(line)
                        self.send_log(line, stage_name, raw_output=True)
                    else:
                        stderr_lines.append(line)
                        self.send_log(line, stage_name, raw_output=True)

            # 读取剩余输出
            for line in process.stdout:
                line = line.rstrip()
                stdout_lines.append(line)
                self.send_log(line, stage_name, raw_output=True)

            for line in process.stderr:
                line = line.rstrip()
                stderr_lines.append(line)
                self.send_log(line, stage_name, raw_output=True)

            # 如果命令成功执行，捕获可能设置的环境变量
            if process.returncode == 0:
                # 通过执行 env 命令获取当前环境变量
                # 确保 env 命令也在正确的环境中执行
                env_cmd = f"source {self.vars_file} 2>/dev/null && env"
                env_process = subprocess.Popen(
                    ['/bin/bash', '-c', env_cmd],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    cwd=self.build_path,
                    env=self.env, # <-- 关键：传递包含 Agent 变量的 env
                    universal_newlines=True
                )
                env_stdout, _ = env_process.communicate()

                # 解析环境变量输出
                if env_process.returncode == 0 and env_stdout:
                    new_vars = {}
                    for line in env_stdout.splitlines():
                        if '=' in line:
                            key, value = line.split('=', 1)
                            # 只处理有变化的变量
                            if key not in self.env or self.env[key] != value:
                                # 排除系统变量
                                if not key.startswith(('_', 'BASH_', 'SHELL', 'HOME', 'PATH', 'PWD', 'OLDPWD')):
                                    new_vars[key] = value
                                    self.env[key] = value

                    # 保存新变量到文件
                    if new_vars:
                        self._save_variables_to_file(new_vars)

            return process.returncode == 0

        except Exception as e:
            self.send_log(f"执行命令失败: {str(e)}", stage_name)
            return False

    def _execute_inline_script(self, stage: Dict[str, Any], check_termination: Callable = None) -> bool:
        """
        执行内联脚本
        Args:
            stage: 阶段配置信息
            check_termination: 检查是否终止的回调函数
        Returns:
            bool: 执行是否成功
        """
        stage_name = stage.get('name', '未命名阶段')
        try:
            script_content = stage.get('script', '').strip()
            if not script_content:
                self.send_log("脚本内容为空", stage_name)
                return False

            # 分行执行每个命令
            success = True
            for line in script_content.splitlines():
                # 检查是否应该终止
                if check_termination and check_termination():
                    self.send_log("构建已被终止，停止执行后续命令", stage_name)
                    return False

                line = line.strip()
                if line and not line.startswith('#'):
                    # 处理包含 && 的命令
                    commands = line.split('&&')
                    if len(commands) > 1:
                        self.send_log("──────────── 命令组开始 ────────────", stage_name)  # 使用更长的水平线标记命令组开始

                    for i, cmd in enumerate(commands):
                        # 检查是否应该终止
                        if check_termination and check_termination():
                            self.send_log("构建已被终止，停止执行后续命令", stage_name)
                            return False

                        cmd = cmd.strip()
                        if cmd:
                            # 使用简单的数字标记（如果是命令组）
                            prefix = f"{i + 1}. " if len(commands) > 1 else ""
                            self.send_log(f"> {prefix}{cmd}", stage_name)
                            if not self._execute_command(cmd, stage_name, show_command=False, check_termination=check_termination):
                                success = False
                                break

                    if len(commands) > 1:
                        self.send_log("──────────── 命令组结束 ────────────", stage_name)  # 使用更长的水平线标记命令组结束

                    if not success:
                        break

            if success:
                self.send_log("执行成功", stage_name)
            else:
                self.send_log("执行失败", stage_name)

            return success

        except Exception as e:
            self.send_log(f"执行内联脚本时发生错误: {str(e)}", stage_name)
            return False

    def _execute_script_file(self, stage: Dict[str, Any], check_termination: Callable = None) -> bool:
        """
        执行脚本文件
        Args:
            stage: 阶段配置信息
            check_termination: 检查是否终止的回调函数
        Returns:
            bool: 执行是否成功
        """
        stage_name = stage.get('name', '未命名阶段')
        try:
            # 检查是否应该终止
            if check_termination and check_termination():
                self.send_log("构建已被终止，跳过执行脚本文件", stage_name)
                return False

            script_file = stage.get('script_file', '').strip()
            if not script_file:
                self.send_log("脚本文件路径为空", stage_name)
                return False

            # 使用原始脚本文件路径，不进行拼接
            script_path = script_file

            if not os.path.exists(script_path):
                self.send_log(f"脚本文件不存在: {script_path}", stage_name)
                return False

            # 显示要执行的脚本文件
            self.send_log(f"执行脚本文件: {script_file}", stage_name)

            # 设置执行权限
            os.chmod(script_path, 0o755)

            # 使用 source 命令读取变量文件后执行脚本
            # 确保脚本在包含正确环境变量（包括 SSH Agent 变量）的 shell 中执行
            command = f"source {self.vars_file} 2>/dev/null || true; \"{script_path}\"" # 使用分号，并给脚本路径加引号

            # 执行脚本，使用实时流式输出
            process = subprocess.Popen(
                ['/bin/bash', '-c', command],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=self.build_path,  # 工作目录仍然是构建目录
                env=self.env, # <-- 关键：传递包含 Agent 变量的 env
                universal_newlines=True,
                bufsize=1  # 行缓冲，确保输出能够实时获取
            )

            # 实时读取并发送输出
            stdout_lines = []
            stderr_lines = []

            # 非阻塞读取输出
            import select
            import fcntl
            import os

            # 设置非阻塞模式
            for pipe in [process.stdout, process.stderr]:
                fd = pipe.fileno()
                fl = fcntl.fcntl(fd, fcntl.F_GETFL)
                fcntl.fcntl(fd, fcntl.F_SETFL, fl | os.O_NONBLOCK)

            # 持续读取直到进程结束
            while process.poll() is None:
                # 检查是否应该终止
                if check_termination and check_termination():
                    process.terminate()
                    self.send_log("构建已被终止，停止当前脚本", stage_name)
                    return False

                # 使用select监控管道是否有数据可读
                readable, _, _ = select.select([process.stdout, process.stderr], [], [], 0.1)

                for pipe in readable:
                    line = pipe.readline()
                    if not line:
                        continue

                    line = line.rstrip()
                    if pipe == process.stdout:
                        stdout_lines.append(line)
                        self.send_log(line, stage_name, raw_output=True)
                    else:
                        stderr_lines.append(line)
                        self.send_log(line, stage_name, raw_output=True)

            # 读取剩余输出
            for line in process.stdout:
                line = line.rstrip()
                stdout_lines.append(line)
                self.send_log(line, stage_name, raw_output=True)

            for line in process.stderr:
                line = line.rstrip()
                stderr_lines.append(line)
                self.send_log(line, stage_name, raw_output=True)

            # 检查执行结果
            success = process.returncode == 0

            if success:
                # 捕获脚本中可能设置的变量
                # 确保 env 命令也在正确的环境中执行
                env_cmd = f"source {self.vars_file} 2>/dev/null && env"
                env_process = subprocess.Popen(
                    ['/bin/bash', '-c', env_cmd],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    cwd=self.build_path,
                    env=self.env, # <-- 关键：传递包含 Agent 变量的 env
                    universal_newlines=True
                )
                env_stdout, _ = env_process.communicate()

                # 解析环境变量输出
                if env_process.returncode == 0 and env_stdout:
                    new_vars = {}
                    for line in env_stdout.splitlines():
                        if '=' in line:
                            key, value = line.split('=', 1)
                            # 只处理有变化的变量
                            if key not in self.env or self.env[key] != value:
                                # 排除系统变量
                                if not key.startswith(('_', 'BASH_', 'SHELL', 'HOME', 'PATH', 'PWD', 'OLDPWD')):
                                    new_vars[key] = value
                                    self.env[key] = value

                    # 保存新变量到文件
                    if new_vars:
                        self._save_variables_to_file(new_vars)

                self.send_log("执行成功", stage_name)
            else:
                self.send_log(f"执行失败，返回码: {process.returncode}", stage_name)

            return success

        except Exception as e:
            self.send_log(f"执行脚本文件时发生错误: {str(e)}", stage_name)
            return False

    def execute_stages(self, stages: List[Dict[str, Any]], check_termination: Callable = None) -> bool:
        """
        执行所有构建阶段
        Args:
            stages: 阶段配置列表
            check_termination: 检查是否终止的回调函数
        Returns:
            bool: 所有阶段是否都执行成功
        """
        if not stages:
            self.send_log("没有配置构建阶段")
            return False

        # 在执行各阶段前先更新环境变量
        self._update_env_from_vars_file()

        for stage in stages:
            # 检查是否应该终止
            if check_termination and check_termination():
                self.send_log("构建已被终止，跳过后续阶段", "Build Stages")
                return False

            stage_name = stage.get('name', '未命名阶段')
            self.send_log(f"开始执行阶段: {stage_name}", "Build Stages")

            # 执行当前阶段
            if not self.execute_stage(stage, check_termination):
                self.send_log(f"阶段 {stage_name} 执行失败", "Build Stages")
                return False

            self.send_log(f"阶段 {stage_name} 执行完成", "Build Stages")

            # 更新环境变量，确保下一个阶段能获取当前阶段设置的变量
            self._update_env_from_vars_file()

        self.send_log("所有阶段执行完成", "Build Stages")
        return True