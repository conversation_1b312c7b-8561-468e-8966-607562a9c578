import os
import json
import uuid
import shutil
import logging
import subprocess
import datetime
import math
from pathlib import Path

from django.http import JsonResponse
from django.views import View

# 计算文件在磁盘上占用的空间（类似 du 命令）
def get_disk_usage(file_path):
    """
    计算文件在磁盘上占用的空间，类似 du 命令
    默认块大小为 4KB (4096 bytes)
    """
    if not os.path.exists(file_path):
        return 0

    # 获取文件的实际大小
    file_size = os.path.getsize(file_path)

    # 计算文件占用的块数（向上取整）
    block_size = 4096  # 4KB，大多数文件系统的默认块大小
    blocks = math.ceil(file_size / block_size)

    # 计算文件在磁盘上占用的空间
    disk_usage = blocks * block_size

    return disk_usage
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.db import transaction
from django.conf import settings

from ..models import BackupConfig, BackupHistory, User
from ..utils.auth import jwt_auth_required

logger = logging.getLogger('apps')

# 生成唯一ID
def generate_id():
    return uuid.uuid4().hex


@method_decorator(csrf_exempt, name='dispatch')
class BackupConfigView(View):
    @method_decorator(jwt_auth_required)
    def get(self, request):
        """获取备份配置"""
        try:
            # 获取或创建备份配置
            config, created = BackupConfig.objects.get_or_create(id=1)

            # 如果是新创建的配置，设置默认备份目录
            if created:
                default_backup_dir = os.path.join(settings.BASE_DIR, 'backups')
                config.backup_dir = default_backup_dir
                config.save()

            return JsonResponse({
                'code': 200,
                'message': '获取备份配置成功',
                'data': {
                    'backup_dir': config.backup_dir,
                    'max_backups': config.max_backups,
                    'enable_scheduled_backup': config.enable_scheduled_backup,
                    'backup_frequency': config.backup_frequency,
                    'backup_time': config.backup_time,
                    'backup_day': config.backup_day,
                    'backup_date': config.backup_date
                }
            })
        except Exception as e:
            logger.error(f'获取备份配置失败: {str(e)}', exc_info=True)
            return JsonResponse({
                'code': 500,
                'message': f'服务器错误: {str(e)}'
            })

    @method_decorator(jwt_auth_required)
    def post(self, request):
        """保存备份配置"""
        try:
            data = json.loads(request.body)

            # 获取或创建备份配置
            config, created = BackupConfig.objects.get_or_create(id=1)

            # 更新配置
            if 'backup_dir' in data:
                config.backup_dir = data['backup_dir']
            if 'max_backups' in data:
                config.max_backups = data['max_backups']
            if 'enable_scheduled_backup' in data:
                config.enable_scheduled_backup = data['enable_scheduled_backup']
            if 'backup_frequency' in data:
                config.backup_frequency = data['backup_frequency']
            if 'backup_time' in data:
                config.backup_time = data['backup_time']
            if 'backup_day' in data:
                config.backup_day = data['backup_day']
            if 'backup_date' in data:
                config.backup_date = data['backup_date']

            # 保存配置
            config.save()

            # 确保备份目录存在
            if config.backup_dir:
                os.makedirs(config.backup_dir, exist_ok=True)

            return JsonResponse({
                'code': 200,
                'message': '保存备份配置成功'
            })
        except Exception as e:
            logger.error(f'保存备份配置失败: {str(e)}', exc_info=True)
            return JsonResponse({
                'code': 500,
                'message': f'服务器错误: {str(e)}'
            })


def sync_backup_files_with_db(backup_dir):
    """
    同步备份文件和数据库记录
    1. 删除数据库中存在但文件不存在的记录
    2. 添加文件存在但数据库中不存在的记录
    """
    if not backup_dir or not os.path.exists(backup_dir):
        return

    try:
        # 获取所有数据库记录
        db_backups = {backup.backup_name: backup for backup in BackupHistory.objects.all()}

        # 获取所有备份文件
        file_backups = set()
        for file in os.listdir(backup_dir):
            if (file.endswith('.sql.gz') or file.endswith('.sql')) and file.startswith('backup_'):
                file_backups.add(file)
                file_path = os.path.join(backup_dir, file)

                # 如果文件存在但数据库中不存在，添加记录
                if file not in db_backups:
                    file_size = get_disk_usage(file_path)
                    file_time = os.path.getmtime(file_path)
                    create_time = datetime.datetime.fromtimestamp(file_time)

                    # 生成备份ID
                    backup_id = generate_id()

                    # 创建新记录
                    BackupHistory.objects.create(
                        backup_id=backup_id,
                        backup_name=file,
                        backup_path=file_path,
                        backup_size=file_size,
                        backup_type='manual',
                        backup_content=['database'],
                        status='success',
                        creator=None  # 无法确定创建者
                    )

        # 删除数据库中存在但文件不存在的记录
        for backup_name, backup in db_backups.items():
            if backup_name not in file_backups:
                backup.delete()

    except Exception as e:
        logger.error(f'同步备份文件和数据库记录失败: {str(e)}', exc_info=True)


@method_decorator(csrf_exempt, name='dispatch')
class BackupHistoryView(View):
    @method_decorator(jwt_auth_required)
    def get(self, request):
        """获取备份历史列表"""
        try:
            # 获取备份配置
            config, _ = BackupConfig.objects.get_or_create(id=1)
            backup_dir = config.backup_dir

            if not backup_dir or not os.path.exists(backup_dir):
                return JsonResponse({
                    'code': 200,
                    'message': '获取备份历史列表成功',
                    'data': []
                })

            # 同步备份文件和数据库记录
            sync_backup_files_with_db(backup_dir)

            # 从数据库获取备份历史
            backups = BackupHistory.objects.all().order_by('-create_time')

            # 格式化输出
            backup_list = []
            for backup in backups:
                # 检查文件是否存在
                if backup.backup_path and os.path.exists(backup.backup_path):
                    backup_list.append({
                        'backup_id': backup.backup_id,
                        'backup_name': backup.backup_name,
                        'backup_size': backup.backup_size,
                        'backup_content': backup.backup_content,
                        'create_time': backup.create_time.strftime('%Y-%m-%d %H:%M:%S') if backup.create_time else ''
                    })

            return JsonResponse({
                'code': 200,
                'message': '获取备份历史列表成功',
                'data': backup_list
            })
        except Exception as e:
            logger.error(f'获取备份历史列表失败: {str(e)}', exc_info=True)
            return JsonResponse({
                'code': 500,
                'message': f'服务器错误: {str(e)}'
            })

    @method_decorator(jwt_auth_required)
    def delete(self, request):
        """删除备份"""
        try:
            data = json.loads(request.body)
            backup_id = data.get('backup_id')

            if not backup_id:
                return JsonResponse({
                    'code': 400,
                    'message': '备份ID不能为空'
                })

            # 获取备份配置
            config, _ = BackupConfig.objects.get_or_create(id=1)
            backup_dir = config.backup_dir

            if not backup_dir or not os.path.exists(backup_dir):
                return JsonResponse({
                    'code': 404,
                    'message': '备份目录不存在'
                })

            # 查找对应的备份文件
            backup_file = None

            # 首先尝试从数据库记录中获取文件名
            try:
                backup_record = BackupHistory.objects.filter(backup_id=backup_id).first()
                if backup_record and backup_record.backup_name:
                    backup_file_path = os.path.join(backup_dir, backup_record.backup_name)
                    if os.path.exists(backup_file_path):
                        backup_file = backup_record.backup_name
            except Exception as e:
                logger.error(f'从数据库获取备份文件失败: {str(e)}', exc_info=True)

            # 如果从数据库记录中没有找到，则扫描目录
            if not backup_file:
                for file in os.listdir(backup_dir):
                    if (file.endswith('.sql.gz') or file.endswith('.sql')) and file.startswith('backup_'):
                        # 尝试从文件名中提取ID
                        if backup_id in file:
                            backup_file = file
                            break

            if not backup_file:
                return JsonResponse({
                    'code': 404,
                    'message': '备份文件不存在'
                })

            # 删除备份文件
            backup_path = os.path.join(backup_dir, backup_file)
            try:
                # 删除文件
                os.remove(backup_path)

                # 同步删除数据库记录
                try:
                    backup_record = BackupHistory.objects.filter(backup_id=backup_id).first()
                    if backup_record:
                        backup_record.delete()
                except Exception as e:
                    logger.error(f'删除备份记录失败: {str(e)}', exc_info=True)

                return JsonResponse({
                    'code': 200,
                    'message': '删除备份成功'
                })
            except Exception as e:
                logger.error(f'删除备份文件失败: {str(e)}', exc_info=True)
                return JsonResponse({
                    'code': 500,
                    'message': f'删除备份文件失败: {str(e)}'
                })
        except Exception as e:
            logger.error(f'删除备份失败: {str(e)}', exc_info=True)
            return JsonResponse({
                'code': 500,
                'message': f'服务器错误: {str(e)}'
            })


@method_decorator(csrf_exempt, name='dispatch')
class BackupCreateView(View):
    @method_decorator(jwt_auth_required)
    def post(self, request):
        """创建备份"""
        try:
            data = json.loads(request.body)
            backup_content = data.get('backup_content', ['database'])
            backup_dir = data.get('backup_dir')
            backup_type = data.get('backup_type', 'manual')

            # 如果没有指定备份目录，使用配置中的备份目录
            if not backup_dir:
                config, _ = BackupConfig.objects.get_or_create(id=1)
                backup_dir = config.backup_dir

            # 确保备份目录存在
            if not backup_dir:
                return JsonResponse({
                    'code': 400,
                    'message': '备份目录不能为空'
                })

            os.makedirs(backup_dir, exist_ok=True)

            # 获取数据库配置
            db_config_file = settings.DATABASES['default']['OPTIONS']['read_default_file']

            # 读取数据库配置
            db_host = 'localhost'
            db_port = '3306'
            db_name = 'liteops'
            db_user = 'root'
            db_password = ''

            with open(db_config_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('host'):
                        db_host = line.split('=')[1].strip()
                    elif line.startswith('port'):
                        db_port = line.split('=')[1].strip()
                    elif line.startswith('database'):
                        db_name = line.split('=')[1].strip()
                    elif line.startswith('user'):
                        db_user = line.split('=')[1].strip()
                    elif line.startswith('password'):
                        db_password = line.split('=')[1].strip()

            # 生成备份文件名
            timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
            backup_id = generate_id()
            backup_name = f"backup_{timestamp}_{db_name}.sql.gz"
            backup_path = os.path.join(backup_dir, backup_name)
            temp_sql_path = os.path.join(backup_dir, f"temp_{timestamp}.sql")

            # 获取数据库配置
            db_config_file = settings.DATABASES['default']['OPTIONS']['read_default_file']

            # 读取数据库配置
            db_host = 'localhost'
            db_port = '3306'
            db_name = 'liteops'
            db_user = 'root'
            db_password = ''

            with open(db_config_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('host'):
                        db_host = line.split('=')[1].strip()
                    elif line.startswith('port'):
                        db_port = line.split('=')[1].strip()
                    elif line.startswith('database'):
                        db_name = line.split('=')[1].strip()
                    elif line.startswith('user'):
                        db_user = line.split('=')[1].strip()
                    elif line.startswith('password'):
                        db_password = line.split('=')[1].strip()

            # 执行数据库备份
            try:
                # 使用 mysqldump 命令备份数据库，使用配置文件中的参数
                cmd = [
                    'mysqldump',
                    f'--host={db_host}',
                    f'--port={db_port}',
                    f'--user={db_user}',
                    f'--password={db_password}',
                    '--single-transaction',
                    '--routines',
                    '--triggers',
                    '--events',
                    db_name
                ]

                # 先将数据库备份到临时SQL文件
                with open(temp_sql_path, 'w') as f:
                    subprocess.run(cmd, stdout=f, check=True)

                # 压缩SQL文件
                import gzip
                with open(temp_sql_path, 'rb') as f_in:
                    with gzip.open(backup_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)

                # 删除临时SQL文件
                if os.path.exists(temp_sql_path):
                    os.remove(temp_sql_path)

                # 获取备份文件在磁盘上占用的空间
                backup_size = get_disk_usage(backup_path)

                # 创建备份历史记录
                creator = User.objects.get(user_id=request.user_id)
                backup = BackupHistory.objects.create(
                    backup_id=backup_id,
                    backup_name=backup_name,
                    backup_path=backup_path,
                    backup_size=backup_size,
                    backup_type=backup_type,
                    backup_content=backup_content,
                    status='success',
                    creator=creator
                )

                # 清理旧备份
                self._cleanup_old_backups()

                return JsonResponse({
                    'code': 200,
                    'message': '创建备份成功',
                    'data': {
                        'backup_id': backup.backup_id,
                        'backup_name': backup.backup_name,
                        'backup_size': backup.backup_size
                    }
                })
            except Exception as e:
                logger.error(f'执行备份失败: {str(e)}', exc_info=True)

                # 创建失败记录
                creator = User.objects.get(user_id=request.user_id)
                BackupHistory.objects.create(
                    backup_id=backup_id,
                    backup_name=backup_name,
                    backup_path=backup_path if os.path.exists(backup_path) else None,
                    backup_size=get_disk_usage(backup_path) if os.path.exists(backup_path) else 0,
                    backup_type=backup_type,
                    backup_content=backup_content,
                    status='failed',
                    error_message=str(e),
                    creator=creator
                )

                return JsonResponse({
                    'code': 500,
                    'message': f'执行备份失败: {str(e)}'
                })
        except Exception as e:
            logger.error(f'创建备份失败: {str(e)}', exc_info=True)
            return JsonResponse({
                'code': 500,
                'message': f'服务器错误: {str(e)}'
            })

    def _cleanup_old_backups(self):
        """清理旧备份"""
        try:
            # 获取备份配置
            config, _ = BackupConfig.objects.get_or_create(id=1)
            max_backups = config.max_backups

            # 获取所有成功的备份，按创建时间降序排序
            backups = BackupHistory.objects.filter(status='success').order_by('-create_time')

            # 如果备份数量超过最大保留数量，删除最旧的备份
            if backups.count() > max_backups:
                for backup in backups[max_backups:]:
                    # 删除备份文件
                    if backup.backup_path and os.path.exists(backup.backup_path):
                        try:
                            os.remove(backup.backup_path)
                        except Exception as e:
                            logger.error(f'删除旧备份文件失败: {str(e)}', exc_info=True)

                    # 删除备份记录
                    backup.delete()
        except Exception as e:
            logger.error(f'清理旧备份失败: {str(e)}', exc_info=True)


@method_decorator(csrf_exempt, name='dispatch')
class BackupRestoreView(View):
    @method_decorator(jwt_auth_required)
    def post(self, request):
        """恢复备份"""
        try:
            data = json.loads(request.body)
            backup_id = data.get('backup_id')

            if not backup_id:
                return JsonResponse({
                    'code': 400,
                    'message': '备份ID不能为空'
                })

            try:
                backup = BackupHistory.objects.get(backup_id=backup_id)
            except BackupHistory.DoesNotExist:
                return JsonResponse({
                    'code': 404,
                    'message': '备份不存在'
                })

            # 检查备份状态
            if backup.status != 'success':
                return JsonResponse({
                    'code': 400,
                    'message': '只能恢复成功的备份'
                })

            # 检查备份文件是否存在
            if not backup.backup_path or not os.path.exists(backup.backup_path):
                return JsonResponse({
                    'code': 400,
                    'message': '备份文件不存在'
                })

            # 获取数据库配置
            db_config_file = settings.DATABASES['default']['OPTIONS']['read_default_file']

            # 读取数据库配置
            db_host = 'localhost'
            db_port = '3306'
            db_name = 'liteops'
            db_user = 'root'
            db_password = ''

            with open(db_config_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('host'):
                        db_host = line.split('=')[1].strip()
                    elif line.startswith('port'):
                        db_port = line.split('=')[1].strip()
                    elif line.startswith('database'):
                        db_name = line.split('=')[1].strip()
                    elif line.startswith('user'):
                        db_user = line.split('=')[1].strip()
                    elif line.startswith('password'):
                        db_password = line.split('=')[1].strip()

            # 执行数据库恢复
            try:
                # 使用 mysql 命令恢复数据库，使用配置文件中的参数
                cmd = [
                    'mysql',
                    f'--host={db_host}',
                    f'--port={db_port}',
                    f'--user={db_user}',
                    f'--password={db_password}',
                    db_name
                ]

                # 如果是压缩文件，先解压
                import gzip
                import tempfile

                if backup.backup_path.endswith('.gz'):
                    # 创建临时文件
                    with tempfile.NamedTemporaryFile(delete=False, suffix='.sql') as temp_file:
                        temp_path = temp_file.name

                    # 解压缩到临时文件
                    with gzip.open(backup.backup_path, 'rb') as f_in:
                        with open(temp_path, 'wb') as f_out:
                            shutil.copyfileobj(f_in, f_out)

                    # 从临时文件恢复
                    with open(temp_path, 'r') as f:
                        subprocess.run(cmd, stdin=f, check=True)

                    # 删除临时文件
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
                else:
                    # 直接从SQL文件恢复
                    with open(backup.backup_path, 'r') as f:
                        subprocess.run(cmd, stdin=f, check=True)

                return JsonResponse({
                    'code': 200,
                    'message': '恢复备份成功'
                })
            except Exception as e:
                logger.error(f'执行恢复失败: {str(e)}', exc_info=True)
                return JsonResponse({
                    'code': 500,
                    'message': f'执行恢复失败: {str(e)}'
                })
        except Exception as e:
            logger.error(f'恢复备份失败: {str(e)}', exc_info=True)
            return JsonResponse({
                'code': 500,
                'message': f'服务器错误: {str(e)}'
            })