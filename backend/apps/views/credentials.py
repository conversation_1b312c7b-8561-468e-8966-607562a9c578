import json
import uuid
import hashlib
import logging
# ！！！重要安全警告！！！
# 以下代码需要引入对称加密库（如 cryptography）来实现私钥和密码的安全存储。
# 当前使用 make_password 的方式（单向哈希）或明文存储是极其不安全的。
# from cryptography.fernet import Fernet # 示例：需要安装和配置
# from django.conf import settings # 示例：需要配置加密密钥

from django.http import JsonResponse
from django.views import View
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.hashers import make_password, check_password # check_password 用于验证，但不能解密
from ..models import (
    GitlabTokenCredential,
    DockerCredential,
    KubernetesCredential,
    SSHKeyCredential, # <-- 导入新模型
    User
)
from ..utils.auth import jwt_auth_required

logger = logging.getLogger('apps')

# ！！！重要安全警告！！！
# 这个函数目前使用了 Django 的 make_password，这是一个单向哈希函数，
# 适用于密码验证，但不适用于需要解密的敏感数据（如 SSH 私钥、密码）。
# 必须替换为可逆的对称加密实现。
# 示例：
# def get_fernet():
#     key = settings.CREDENTIAL_ENCRYPTION_KEY.encode() # 从配置加载主密钥
#     return Fernet(key)
#
# def encrypt_sensitive_data(data):
#     if not data:
#         return None
#     fernet = get_fernet()
#     return fernet.encrypt(data.encode()).decode()
#
# def decrypt_sensitive_data(encrypted_data):
#     if not encrypted_data:
#         return None
#     fernet = get_fernet()
#     return fernet.decrypt(encrypted_data.encode()).decode()

def generate_id():
    """生成唯一ID"""
    return hashlib.sha256(str(uuid.uuid4()).encode()).hexdigest()[:32]

def encrypt_sensitive_data(data, credential_type=None):
    """加密敏感数据 (当前实现不安全，仅为占位)"""
    if not data:
        return None
    # GitLab Token 和 SSH 私钥/密码不应使用单向哈希
    if credential_type in ['gitlab_token', 'ssh_key']:
        logger.warning(f"正在为 {credential_type} 类型的敏感数据使用不安全的存储方式！")
        # ！！！生产环境必须替换为对称加密！！！
        return data # 临时明文存储 (极不安全)
    # 其他类型（如 docker password, kubeconfig）暂时使用 make_password (虽然也不理想，但比明文好一点)
    # 但 kubeconfig 可能也需要解密才能使用，所以 make_password 可能不适用
    logger.warning(f"正在为 {credential_type} 类型的敏感数据使用 make_password (可能不适用)！")
    return make_password(data)

CREDENTIAL_MODELS = {
    'gitlab_token': GitlabTokenCredential,
    'docker': DockerCredential,
    'kubernetes': KubernetesCredential,
    'ssh_key': SSHKeyCredential, # <-- 添加新模型
}

@method_decorator(csrf_exempt, name='dispatch')
class CredentialView(View):
    @method_decorator(jwt_auth_required)
    def get(self, request):
        """获取凭据列表"""
        try:
            credential_type = request.GET.get('type')
            if credential_type not in CREDENTIAL_MODELS:
                return JsonResponse({
                    'code': 400,
                    'message': '无效的凭据类型'
                })

            model = CREDENTIAL_MODELS[credential_type]
            credentials = model.objects.all()
            
            data = []
            for credential in credentials:
                item = {
                    'credential_id': credential.credential_id,
                    'name': credential.name,
                    'description': credential.description,
                    'creator': {
                        'user_id': credential.creator.user_id,
                        'name': credential.creator.name
                    },
                    'create_time': credential.create_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'update_time': credential.update_time.strftime('%Y-%m-%d %H:%M:%S'),
                }

                # 根据不同凭据类型添加特定字段
                if credential_type == 'gitlab':
                    item['username'] = credential.username
                elif credential_type == 'docker':
                    item['registry'] = credential.registry
                    item['username'] = credential.username
                elif credential_type == 'ssh_key':
                    item['username'] = credential.username # 返回用户名

                data.append(item)

            return JsonResponse({
                'code': 200,
                'message': '获取凭据列表成功',
                'data': data
            })
        except Exception as e:
            logger.error(f'获取凭据列表失败: {str(e)}', exc_info=True)
            return JsonResponse({
                'code': 500,
                'message': f'服务器错误: {str(e)}'
            })

    @method_decorator(jwt_auth_required)
    def post(self, request):
        """创建凭据"""
        try:
            data = json.loads(request.body)
            credential_type = data.get('type')
            
            if credential_type not in CREDENTIAL_MODELS:
                return JsonResponse({
                    'code': 400,
                    'message': '无效的凭据类型'
                })

            # 获取当前用户
            try:
                creator = User.objects.get(user_id=request.user_id)
            except User.DoesNotExist:
                return JsonResponse({
                    'code': 400,
                    'message': '用户不存在'
                })

            model = CREDENTIAL_MODELS[credential_type]
            credential = model(
                credential_id=generate_id(),
                name=data.get('name'),
                description=data.get('description'),
                creator=creator
            )

            # 根据不同凭据类型设置特定字段
            if credential_type == 'gitlab':
                credential.username = data.get('username')
                credential.password = encrypt_sensitive_data(data.get('password'), credential_type)
            elif credential_type == 'gitlab_token':
                credential.token = data.get('token')  # GitLab Token 不加密
            elif credential_type == 'docker':
                credential.registry = data.get('registry')
                credential.username = data.get('username')
                credential.password = encrypt_sensitive_data(data.get('password'), credential_type)
            elif credential_type == 'kubernetes':
                credential.kubeconfig = encrypt_sensitive_data(data.get('kubeconfig'), credential_type)
            elif credential_type == 'ssh_key':
                credential.username = data.get('username')
                # ！！！警告：以下是明文存储，极不安全，需要替换为对称加密！！！
                credential.private_key = data.get('private_key') # 需要加密
                credential.passphrase = data.get('passphrase') # 需要加密

            credential.save()

            return JsonResponse({
                'code': 200,
                'message': '创建凭据成功',
                'data': {
                    'credential_id': credential.credential_id
                }
            })
        except Exception as e:
            logger.error(f'创建凭据失败: {str(e)}', exc_info=True)
            return JsonResponse({
                'code': 500,
                'message': f'服务器错误: {str(e)}'
            })

    @method_decorator(jwt_auth_required)
    def put(self, request):
        """更新凭据"""
        try:
            data = json.loads(request.body)
            credential_type = data.get('type')
            credential_id = data.get('credential_id')

            if not credential_id:
                return JsonResponse({
                    'code': 400,
                    'message': '凭据ID不能为空'
                })

            if credential_type not in CREDENTIAL_MODELS:
                return JsonResponse({
                    'code': 400,
                    'message': '无效的凭据类型'
                })

            model = CREDENTIAL_MODELS[credential_type]
            try:
                credential = model.objects.get(credential_id=credential_id)
            except model.DoesNotExist:
                return JsonResponse({
                    'code': 404,
                    'message': '凭据不存在'
                })

            # 更新基本字段
            credential.name = data.get('name', credential.name)
            credential.description = data.get('description', credential.description)

            # 根据不同凭据类型更新特定字段
            if credential_type == 'gitlab':
                credential.username = data.get('username', credential.username)
                if 'password' in data:  # 只在提供新密码时更新
                    credential.password = encrypt_sensitive_data(data['password'], credential_type)
            elif credential_type == 'gitlab_token':
                if 'token' in data:  # 只在提供新token时更新
                    credential.token = data['token']  # GitLab Token 不加密
            elif credential_type == 'docker':
                credential.registry = data.get('registry', credential.registry)
                credential.username = data.get('username', credential.username)
                if 'password' in data:  # 只在提供新密码时更新
                    credential.password = encrypt_sensitive_data(data['password'], credential_type)
            elif credential_type == 'kubernetes':
                if 'kubeconfig' in data:  # 只在提供新kubeconfig时更新
                    credential.kubeconfig = encrypt_sensitive_data(data['kubeconfig'], credential_type)
            elif credential_type == 'ssh_key':
                credential.username = data.get('username', credential.username)
                if 'private_key' in data: # 只在提供新私钥时更新
                    # ！！！警告：以下是明文存储，极不安全，需要替换为对称加密！！！
                    credential.private_key = data['private_key']
                if 'passphrase' in data: # 只在提供新密码时更新
                    # ！！！警告：以下是明文存储，极不安全，需要替换为对称加密！！！
                    credential.passphrase = data['passphrase']

            credential.save()

            return JsonResponse({
                'code': 200,
                'message': '更新凭据成功'
            })
        except Exception as e:
            logger.error(f'更新凭据失败: {str(e)}', exc_info=True)
            return JsonResponse({
                'code': 500,
                'message': f'服务器错误: {str(e)}'
            })

    @method_decorator(jwt_auth_required)
    def delete(self, request):
        """删除凭据"""
        try:
            data = json.loads(request.body)
            credential_type = data.get('type')
            credential_id = data.get('credential_id')

            if not credential_id:
                return JsonResponse({
                    'code': 400,
                    'message': '凭据ID不能为空'
                })

            if credential_type not in CREDENTIAL_MODELS:
                return JsonResponse({
                    'code': 400,
                    'message': '无效的凭据类型'
                })

            model = CREDENTIAL_MODELS[credential_type]
            try:
                credential = model.objects.get(credential_id=credential_id)
                credential.delete()
            except model.DoesNotExist:
                return JsonResponse({
                    'code': 404,
                    'message': '凭据不存在'
                })

            return JsonResponse({
                'code': 200,
                'message': '删除凭据成功'
            })
        except Exception as e:
            logger.error(f'删除凭据失败: {str(e)}', exc_info=True)
            return JsonResponse({
                'code': 500,
                'message': f'服务器错误: {str(e)}'
            }) 