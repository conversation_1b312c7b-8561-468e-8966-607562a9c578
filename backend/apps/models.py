from django.db import models

class User(models.Model):
    """
    用户表
    """
    id = models.AutoField(primary_key=True)
    user_id = models.CharField(max_length=32, unique=True, null=True, verbose_name='用户ID')
    username = models.CharField(max_length=50, unique=True, null=True, verbose_name='用户名')
    name = models.CharField(max_length=50, null=True, verbose_name='姓名')
    password = models.CharField(max_length=128, null=True, verbose_name='密码')
    email = models.EmailField(max_length=100, unique=True, null=True, verbose_name='邮箱')
    status = models.SmallIntegerField(null=True, verbose_name='状态')
    login_time = models.DateTimeField(null=True, verbose_name='最后登录时间')
    create_time = models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')

    class Meta:
        db_table = 'user'
        verbose_name = '用户'
        verbose_name_plural = verbose_name
        ordering = ['-create_time']

    def __str__(self):
        return self.username


class Role(models.Model):
    """
    角色表
    """
    id = models.AutoField(primary_key=True)
    role_id = models.CharField(max_length=32, unique=True, null=True, verbose_name='角色ID')
    name = models.CharField(max_length=50, unique=True, null=True, verbose_name='角色名称')
    description = models.TextField(null=True, blank=True, verbose_name='角色描述')
    permissions = models.JSONField(default=dict, null=True, verbose_name='权限配置')
    creator = models.ForeignKey('User', on_delete=models.CASCADE, to_field='user_id', null=True, verbose_name='创建者')
    create_time = models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')

    class Meta:
        db_table = 'role'
        verbose_name = '角色'
        verbose_name_plural = verbose_name
        ordering = ['-create_time']

    def __str__(self):
        return self.name


class UserRole(models.Model):
    """
    用户角色关联表
    """
    id = models.AutoField(primary_key=True)
    user = models.ForeignKey('User', on_delete=models.CASCADE, to_field='user_id', null=True, verbose_name='用户')
    role = models.ForeignKey('Role', on_delete=models.CASCADE, to_field='role_id', null=True, verbose_name='角色')
    create_time = models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')

    class Meta:
        db_table = 'user_role'
        verbose_name = '用户角色关联'
        verbose_name_plural = verbose_name
        ordering = ['-create_time']
        unique_together = ['user', 'role']  # 确保用户和角色的组合唯一

    def __str__(self):
        return f"{self.user.username} - {self.role.name}"


class UserToken(models.Model):
    """
    用户Token表
    """
    id = models.AutoField(primary_key=True)
    token_id = models.CharField(max_length=32, unique=True, null=True, verbose_name='TokenID')
    user = models.ForeignKey('User', on_delete=models.CASCADE, to_field='user_id', null=True, verbose_name='用户')
    token = models.CharField(max_length=256, null=True, verbose_name='Token信息')
    create_time = models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')

    class Meta:
        db_table = 'user_token'
        verbose_name = '用户Token'
        verbose_name_plural = verbose_name
        ordering = ['-create_time']

    def __str__(self):
        return f"{self.user.username}'s token"


class Project(models.Model):
    """
    项目表 - 包含项目基本信息和服务信息
    """
    id = models.AutoField(primary_key=True)
    project_id = models.CharField(max_length=32, unique=True, null=True, verbose_name='项目ID')
    name = models.CharField(max_length=50, null=True, verbose_name='项目名称')
    description = models.TextField(null=True, blank=True, verbose_name='项目描述')
    category = models.CharField(max_length=20, null=True, verbose_name='服务类别')  # frontend, backend, mobile
    repository = models.CharField(max_length=255, null=True, verbose_name='GitLab仓库地址')
    creator = models.ForeignKey('User', on_delete=models.CASCADE, to_field='user_id', null=True, verbose_name='创建者')
    create_time = models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')

    class Meta:
        db_table = 'project'
        verbose_name = '项目'
        verbose_name_plural = verbose_name
        ordering = ['-create_time']

    def __str__(self):
        return self.name


class GitlabTokenCredential(models.Model):
    """
    GitLab Token凭据表
    """
    id = models.AutoField(primary_key=True)
    credential_id = models.CharField(max_length=32, unique=True, null=True, verbose_name='凭据ID')
    name = models.CharField(max_length=50, null=True, verbose_name='凭据名称')
    description = models.TextField(null=True, blank=True, verbose_name='凭据描述')
    token = models.CharField(max_length=255, null=True, verbose_name='GitLab Token')
    creator = models.ForeignKey('User', on_delete=models.CASCADE, to_field='user_id', null=True, verbose_name='创建者')
    create_time = models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')

    class Meta:
        db_table = 'gitlab_token_credential'
        verbose_name = 'GitLab Token凭据'
        verbose_name_plural = verbose_name
        ordering = ['-create_time']

    def __str__(self):
        return self.name


class DockerCredential(models.Model):
    """
    Docker凭据表
    """
    id = models.AutoField(primary_key=True)
    credential_id = models.CharField(max_length=32, unique=True, null=True, verbose_name='凭据ID')
    name = models.CharField(max_length=50, null=True, verbose_name='凭据名称')
    description = models.TextField(null=True, blank=True, verbose_name='凭据描述')
    registry = models.CharField(max_length=255, null=True, verbose_name='Registry地址')
    username = models.CharField(max_length=50, null=True, verbose_name='用户名')
    password = models.CharField(max_length=255, null=True, verbose_name='密码')
    creator = models.ForeignKey('User', on_delete=models.CASCADE, to_field='user_id', null=True, verbose_name='创建者')
    create_time = models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')

    class Meta:
        db_table = 'docker_credential'
        verbose_name = 'Docker凭据'
        verbose_name_plural = verbose_name
        ordering = ['-create_time']

    def __str__(self):
        return self.name


class KubernetesCredential(models.Model):
    """
    Kubernetes凭据表
    """
    id = models.AutoField(primary_key=True)
    credential_id = models.CharField(max_length=32, unique=True, null=True, verbose_name='凭据ID')
    name = models.CharField(max_length=50, null=True, verbose_name='凭据名称')
    description = models.TextField(null=True, blank=True, verbose_name='凭据描述')
    kubeconfig = models.TextField(null=True, verbose_name='kubeconfig内容')
    creator = models.ForeignKey('User', on_delete=models.CASCADE, to_field='user_id', null=True, verbose_name='创建者')
    create_time = models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')

    class Meta:
        db_table = 'kubernetes_credential'
        verbose_name = 'Kubernetes凭据'
        verbose_name_plural = verbose_name
        ordering = ['-create_time']

    def __str__(self):
        return self.name


class SSHKeyCredential(models.Model):
    """
    SSH密钥凭据表
    """
    id = models.AutoField(primary_key=True)
    credential_id = models.CharField(max_length=32, unique=True, null=True, verbose_name='凭据ID')
    name = models.CharField(max_length=50, null=True, verbose_name='凭据名称')
    description = models.TextField(null=True, blank=True, verbose_name='凭据描述')
    username = models.CharField(max_length=50, null=True, verbose_name='远程用户名')
    # 警告：private_key 和 passphrase 是高度敏感信息，必须使用对称加密（如 AES）进行加密存储，
    # 而不是 Django 的 make_password（单向哈希）。当前实现仅为占位，存在严重安全风险！
    private_key = models.TextField(null=True, verbose_name='SSH私钥内容')
    passphrase = models.CharField(max_length=255, null=True, blank=True, verbose_name='私钥密码 (可选)') # 警告：密码也应加密存储
    creator = models.ForeignKey('User', on_delete=models.CASCADE, to_field='user_id', null=True, verbose_name='创建者')
    create_time = models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')

    class Meta:
        db_table = 'ssh_key_credential'
        verbose_name = 'SSH密钥凭据'
        verbose_name_plural = verbose_name
        ordering = ['-create_time']

    def __str__(self):
        return self.name


class Environment(models.Model):
    """
    环境配置表 - 用于管理不同的部署环境（如开发、测试、预发布、生产等）
    """
    id = models.AutoField(primary_key=True)
    environment_id = models.CharField(max_length=32, unique=True, null=True, verbose_name='环境ID')
    name = models.CharField(max_length=50, null=True, verbose_name='环境名称')
    type = models.CharField(max_length=20, null=True, verbose_name='环境类型')  # development, testing, staging, production
    description = models.TextField(null=True, blank=True, verbose_name='环境描述')
    creator = models.ForeignKey('User', on_delete=models.CASCADE, to_field='user_id', null=True, verbose_name='创建者')
    create_time = models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')

    class Meta:
        db_table = 'environment'
        verbose_name = '环境配置'
        verbose_name_plural = verbose_name
        ordering = ['-create_time']

    def __str__(self):
        return self.name


class BuildTask(models.Model):
    """构建任务表"""
    id = models.AutoField(primary_key=True)
    task_id = models.CharField(max_length=32, unique=True, null=True, verbose_name='任务ID')
    name = models.CharField(max_length=100, null=True, verbose_name='任务名称')
    project = models.ForeignKey('Project', on_delete=models.CASCADE, to_field='project_id', null=True, verbose_name='所属项目')
    environment = models.ForeignKey('Environment', on_delete=models.CASCADE, to_field='environment_id', null=True, verbose_name='构建环境')
    description = models.TextField(null=True, blank=True, verbose_name='任务描述')
    requirement = models.TextField(null=True, blank=True, verbose_name='构建需求描述')
    branch = models.CharField(max_length=100, default='main', null=True, verbose_name='默认分支')
    git_token = models.ForeignKey('GitlabTokenCredential', on_delete=models.SET_NULL, to_field='credential_id', null=True, verbose_name='Git Token')
    ssh_key_credential = models.ForeignKey('SSHKeyCredential', on_delete=models.SET_NULL, to_field='credential_id', null=True, blank=True, verbose_name='SSH密钥凭据')
    version = models.CharField(max_length=50, null=True, blank=True, verbose_name='构建版本号')

    # 构建阶段（使用JSON存储）
    stages = models.JSONField(default=list, verbose_name='构建阶段')

    # 构建时间信息（使用JSON存储）
    build_time = models.JSONField(default=dict, verbose_name='构建时间信息', help_text='''
    {
        "total_duration": "300",  # 总耗时（秒）
        "start_time": "2024-03-06 12:00:00",  # 开始时间
        "end_time": "2024-03-06 12:05:00",  # 结束时间
        "stages_time": [  # 各阶段时间信息
            {
                "name": "代码拉取",
                "start_time": "2024-03-06 12:00:00",
                "duration": "60"  # 耗时（秒）
            }
        ]
    }
    ''')

    # 构建后操作
    notification_channels = models.JSONField(default=list, verbose_name='通知方式')

    # 状态和统计
    status = models.CharField(max_length=20, default='created', null=True, verbose_name='任务状态')  # created, disabled
    building_status = models.CharField(max_length=20, default='idle', null=True, verbose_name='构建状态')  # idle, building
    last_build_number = models.IntegerField(default=0, verbose_name='最后构建号')
    total_builds = models.IntegerField(default=0, verbose_name='总构建次数')
    success_builds = models.IntegerField(default=0, verbose_name='成功构建次数')
    failure_builds = models.IntegerField(default=0, verbose_name='失败构建次数')

    creator = models.ForeignKey('User', on_delete=models.CASCADE, to_field='user_id', null=True, verbose_name='创建者')
    create_time = models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')

    class Meta:
        db_table = 'build_task'
        verbose_name = '构建任务'
        verbose_name_plural = verbose_name
        ordering = ['-create_time']


class BuildHistory(models.Model):
    """构建历史表"""
    id = models.AutoField(primary_key=True)
    history_id = models.CharField(max_length=32, unique=True, null=True, verbose_name='历史ID')
    task = models.ForeignKey('BuildTask', on_delete=models.CASCADE, to_field='task_id', null=True, verbose_name='构建任务')
    build_number = models.IntegerField(verbose_name='构建序号')
    branch = models.CharField(max_length=100, null=True, verbose_name='构建分支')
    commit_id = models.CharField(max_length=40, null=True, verbose_name='Git Commit ID')
    version = models.CharField(max_length=50, null=True, verbose_name='构建版本')
    status = models.CharField(max_length=20, default='pending', verbose_name='构建状态')  # pending, running, success, failed, terminated
    requirement = models.TextField(null=True, blank=True, verbose_name='构建需求描述')
    build_log = models.TextField(null=True, blank=True, verbose_name='构建日志')
    stages = models.JSONField(default=list, verbose_name='构建阶段')
    build_time = models.JSONField(default=dict, verbose_name='构建时间信息', help_text='''
    {
        "total_duration": "300",  # 总耗时（秒）
        "start_time": "2024-03-06 12:00:00",  # 开始时间
        "end_time": "2024-03-06 12:05:00",  # 结束时间
        "stages_time": [  # 各阶段时间信息
            {
                "name": "代码拉取",
                "start_time": "2024-03-06 12:00:00",
                "duration": "60"  # 耗时（秒）
            }
        ]
    }
    ''')

    operator = models.ForeignKey('User', on_delete=models.SET_NULL, to_field='user_id', null=True, verbose_name='构建人')
    create_time = models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')

    class Meta:
        db_table = 'build_history'
        verbose_name = '构建历史'
        verbose_name_plural = verbose_name
        ordering = ['-create_time']
        unique_together = ['task', 'build_number']  # 确保任务和构建号的组合唯一

    def __str__(self):
        return f"{self.task.name} #{self.build_number}"


class NotificationRobot(models.Model):
    """通知机器人表"""
    id = models.AutoField(primary_key=True)
    robot_id = models.CharField(max_length=32, unique=True, null=True, verbose_name='机器人ID')
    type = models.CharField(max_length=20, null=True, verbose_name='机器人类型')  # dingtalk, wecom, feishu
    name = models.CharField(max_length=50, null=True, verbose_name='机器人名称')
    webhook = models.CharField(max_length=255, null=True, verbose_name='Webhook地址')
    security_type = models.CharField(max_length=20, null=True, verbose_name='安全设置类型')  # none, secret, keyword, ip
    secret = models.CharField(max_length=255, null=True, blank=True, verbose_name='加签密钥')
    keywords = models.JSONField(default=list, null=True, blank=True, verbose_name='自定义关键词')
    ip_list = models.JSONField(default=list, null=True, blank=True, verbose_name='IP白名单')
    remark = models.TextField(null=True, blank=True, verbose_name='备注')
    creator = models.ForeignKey('User', on_delete=models.CASCADE, to_field='user_id', null=True, verbose_name='创建者')
    create_time = models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')

    class Meta:
        db_table = 'notification_robot'
        verbose_name = '通知机器人'
        verbose_name_plural = verbose_name
        ordering = ['-create_time']

    def __str__(self):
        return f"{self.name} ({self.type})"


class LoginLog(models.Model):
    """
    登录日志表
    """
    id = models.AutoField(primary_key=True)
    log_id = models.CharField(max_length=32, unique=True, null=True, verbose_name='日志ID')
    user = models.ForeignKey('User', on_delete=models.CASCADE, to_field='user_id', null=True, verbose_name='用户')
    ip_address = models.CharField(max_length=50, null=True, verbose_name='IP地址')
    user_agent = models.TextField(null=True, blank=True, verbose_name='用户代理')
    status = models.CharField(max_length=20, null=True, verbose_name='登录状态')  # success, failed
    fail_reason = models.CharField(max_length=100, null=True, blank=True, verbose_name='失败原因')
    login_time = models.DateTimeField(auto_now_add=True, null=True, verbose_name='登录时间')

    class Meta:
        db_table = 'login_log'
        verbose_name = '登录日志'
        verbose_name_plural = verbose_name
        ordering = ['-login_time']

    def __str__(self):
        return f"{self.user.username} - {self.login_time}"


class BackupConfig(models.Model):
    """
    备份配置表 - 用于存储系统备份配置
    """
    id = models.AutoField(primary_key=True)
    backup_dir = models.CharField(max_length=255, null=True, verbose_name='备份目录')
    max_backups = models.IntegerField(default=10, verbose_name='最大备份数量')
    enable_scheduled_backup = models.BooleanField(default=False, verbose_name='启用定时备份')
    backup_frequency = models.CharField(max_length=20, default='daily', verbose_name='备份频率')  # daily, weekly, monthly
    backup_time = models.CharField(max_length=10, null=True, verbose_name='备份时间')  # HH:MM 格式
    backup_day = models.CharField(max_length=5, default='1', verbose_name='备份日(周几)')  # 0-6，对应周日到周六
    backup_date = models.IntegerField(default=1, verbose_name='备份日期(每月几号)')  # 1-31
    update_time = models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')

    class Meta:
        db_table = 'backup_config'
        verbose_name = '备份配置'
        verbose_name_plural = verbose_name

    def __str__(self):
        return f"备份配置 (ID: {self.id})"


class BackupHistory(models.Model):
    """
    备份历史表 - 用于记录系统备份历史
    """
    id = models.AutoField(primary_key=True)
    backup_id = models.CharField(max_length=32, unique=True, null=True, verbose_name='备份ID')
    backup_name = models.CharField(max_length=100, null=True, verbose_name='备份文件名')
    backup_path = models.CharField(max_length=255, null=True, verbose_name='备份文件路径')
    backup_size = models.BigIntegerField(default=0, verbose_name='备份大小(占用硬盘空间大小)')
    backup_type = models.CharField(max_length=20, default='manual', verbose_name='备份类型')  # manual, scheduled
    backup_content = models.JSONField(default=list, verbose_name='备份内容')  # ['database']
    status = models.CharField(max_length=20, default='success', verbose_name='备份状态')  # success, failed
    error_message = models.TextField(null=True, blank=True, verbose_name='错误信息')
    creator = models.ForeignKey('User', on_delete=models.SET_NULL, to_field='user_id', null=True, verbose_name='创建者')
    create_time = models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')

    class Meta:
        db_table = 'backup_history'
        verbose_name = '备份历史'
        verbose_name_plural = verbose_name
        ordering = ['-create_time']

    def __str__(self):
        return f"{self.backup_name} ({self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else '未知时间'})"